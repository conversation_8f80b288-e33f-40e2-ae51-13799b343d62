import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/utils/attachment_storage_service.dart';
import 'package:s3g/src/manager/instance/data/local/instance_local_datasource.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/data/local/diagnostic_local_datasource.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/data/local/models/diagnostic_local_model.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/data/remote/diagnostic_remote_datasource.dart';
import 'package:s3g/src/manager/questionnaire/data/local/questionnaire_local_datasource.dart';
import 'package:s3g/src/manager/questionnaire/domain/entity/questionnaire.dart';
import 'package:s3g/src/sync/handlers/base_sync_handler.dart';
import 'package:s3g/src/sync/sync_service.dart';

@injectable
class ManagerDiagnosticSyncHandler extends BaseSyncHandler {
  final DiagnosticLocalDataSource _localDataSource;
  final DiagnosticRemoteDataSource _remoteDataSource;
  final InstanceLocalDataSource _instanceLocalDataSource;
  final QuestionnaireLocalDataSource _questionnaireLocalDataSource;
  final AttachmentStorageService _attachmentStorage;

  @override
  String get featureName => 'Manager Diagnostic';

  ManagerDiagnosticSyncHandler({
    required DiagnosticLocalDataSource localDataSource,
    required DiagnosticRemoteDataSource remoteDataSource,
    required InstanceLocalDataSource instanceLocalDataSource,
    required QuestionnaireLocalDataSource questionnaireLocalDataSource,
    required AttachmentStorageService attachmentStorage,
  })  : _localDataSource = localDataSource,
        _remoteDataSource = remoteDataSource,
        _instanceLocalDataSource = instanceLocalDataSource,
        _questionnaireLocalDataSource = questionnaireLocalDataSource,
        _attachmentStorage = attachmentStorage;

  @override
  Future<void> syncAll() async {
    final pendingDiagnostics =
        await _localDataSource.getPendingSyncDiagnostics();
    log('Found ${pendingDiagnostics.length} diagnostics to sync');

    for (final diagnostic in pendingDiagnostics) {
      await _syncSingleDiagnostic(diagnostic);
    }
  }

  Future<void> _syncSingleDiagnostic(DiagnosticLocalModel diagnostic) async {
    try {
      // Get the instance to check if it's synced
      final instance = await _instanceLocalDataSource
          .getInstanceById(diagnostic.instanceLocalId);

      if (instance == null || instance.id == null) {
        log('Instance not found for diagnostic ${diagnostic.localId}');
        return;
      }

      // Skip if instance is not synced yet
      if (instance.syncStatus != SyncStatus.synced.value) {
        log('Instance ${instance.localId} not synced yet (status: ${instance.syncStatus}), skipping diagnostic ${diagnostic.localId}');
        return;
      }

      // Get questionnaire for API calls
      final questionnaire = await _questionnaireLocalDataSource
          .getQuestionnaireById(diagnostic.questionnaireId);

      if (questionnaire == null) {
        log('Questionnaire not found for diagnostic ${diagnostic.localId}');
        await _localDataSource.markDiagnosticSyncFailed(
          diagnostic.localId,
          'Questionnaire introuvable',
        );
        return;
      }

      if (diagnostic.isDeleted) {
        // Handle deletion
        if (diagnostic.id != null) {
          try {
            await _remoteDataSource.deleteDiagnostic(
              instanceId: instance.id!,
              diagnosticId: diagnostic.id!,
            );
          } on DioException catch (e) {
            if (e.response?.statusCode != 404) {
              rethrow;
            }
            // Ignore 404 errors - diagnostic already deleted on server
          }
        }

        // Remove from local after successful sync
        await _localDataSource.deleteDiagnostic(diagnostic.localId);
        log('Successfully deleted diagnostic ${diagnostic.localId}');
        return;
      }

      // Check if response is an attachment and handle it
      final questionnaireEntity = questionnaire.toEntity();
      final hasOfflineAttachment =
          questionnaireEntity.type == QuestionnaireResponseType.ATTACHMENT &&
              _attachmentStorage.isOfflineAttachment(diagnostic.response);

      String? responsePath = diagnostic.response;
      if (hasOfflineAttachment) {
        final attachmentFile =
            await _attachmentStorage.getAttachment(diagnostic.response);
        responsePath = attachmentFile?.path;
      }

      // Handle creation or update
      if (diagnostic.isOfflineCreated) {
        // Create on server
        final response = await _remoteDataSource.createDiagnostic(
          instanceId: instance.id!,
          questionnaire: questionnaireEntity,
          response: responsePath ?? diagnostic.response,
        );

        // Update local with server ID
        await _localDataSource.markDiagnosticAsSynced(
          diagnostic.localId,
          response.id,
        );

        log('Successfully created diagnostic ${diagnostic.localId} on server with id ${response.id}');
      } else {
        // Update on server
        await _remoteDataSource.editDiagnostic(
          instanceId: instance.id!,
          questionnaire: questionnaireEntity,
          diagnosticId: diagnostic.id!,
          response: responsePath ?? diagnostic.response,
        );

        // Mark as synced
        diagnostic.updateSyncStatus(SyncStatus.synced);
        await _localDataSource.saveDiagnostic(diagnostic);

        log('Successfully updated diagnostic ${diagnostic.localId} on server');
      }
    } on DioException catch (e) {
      if (e.response == null) {
        rethrow;
      }

      final response = e.response!;
      final responseStr = response.data.toString();

      // Handle specific error case
      if (response.statusCode == 422 &&
          responseStr.contains('questionnaire_id:') &&
          responseStr.contains('pris')) {
        log("Treatment type already taken, marking as synced ${diagnostic.localId}");
        await _localDataSource.deleteDiagnostic(diagnostic.localId);
      } else {
        await _localDataSource.markDiagnosticSyncFailed(
          diagnostic.localId,
          e.toString(),
        );
      }
    } catch (e) {
      log('Error syncing diagnostic ${diagnostic.localId}: $e');
      await _localDataSource.markDiagnosticSyncFailed(
        diagnostic.localId,
        e.toString(),
      );
    }
  }
}
