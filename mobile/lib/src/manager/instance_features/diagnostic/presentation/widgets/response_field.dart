import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/constants/constants.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/diagnostic.dart';
import 'package:s3g/src/manager/questionnaire/questionnaire.dart';

final dateResponseTypeFormat = DateFormat('dd/MM/yyyy');

class ResponseField extends StatefulWidget {
  final void Function(XFile?) onFileChange;
  final Questionnaire questionnaire;
  final Diagnostic? editable;

  const ResponseField({
    super.key,
    required this.questionnaire,
    required this.onFileChange,
    this.editable,
  });

  @override
  State<ResponseField> createState() => ResponseFieldState();
}

class ResponseFieldState extends State<ResponseField> {
  XFile? _selectedFile;

  @override
  Widget build(BuildContext context) {
    final questionnaire = widget.questionnaire;

    switch (questionnaire.type) {
      case QuestionnaireResponseType.BOOLEAN:
        return FormBuilderChoiceChips<String>(
          name: "response",
          spacing: 15,
          selectedColor:
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
          initialValue: widget.editable?.response,
          validator: FormBuilderValidators.required(),
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            labelText: 'Réponse',
          ),
          options: const [
            FormBuilderChipOption(
              value: "Oui",
              child: Text("Oui"),
            ),
            FormBuilderChipOption(
              value: "Non",
              child: Text("Non"),
            )
          ],
        );

      case QuestionnaireResponseType.ATTACHMENT:
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Show file
            if (widget.editable != null && _selectedFile == null) ...[
              columnSizedBox,
              AttachmentViewer(attachment: widget.editable!.response),
              columnSizedBox,
              columnSizedBox,
            ],

            // File picker
            FilePhotoPicker(onFileChange: (file) {
              widget.onFileChange(file);

              setState(() {
                _selectedFile = file;
              });
            }),
          ],
        );

      case QuestionnaireResponseType.CHOICES:
        List<QuestionnaireChoice> choicesList = questionnaire.choices ?? [];

        return FormBuilderDropdown<String>(
          name: "response",
          initialValue: widget.editable?.response,
          validator: FormBuilderValidators.required(),
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            labelText: 'Réponse',
          ),
          items: choicesList.map((choice) {
            return DropdownMenuItem(
              value: choice.choice,
              child: Text(choice.choice),
            );
          }).toList(),
        );

      case QuestionnaireResponseType.DATE:
        return FormBuilderDateTimePicker(
          name: 'response',
          valueTransformer: (DateTime? value) {
            return value != null ? dateResponseTypeFormat.format(value) : null;
          },
          initialValue: widget.editable?.response != null
              ? dateResponseTypeFormat.parse(widget.editable!.response)
              : null,
          format: dateResponseTypeFormat,
          inputType: InputType.date,
          validator: FormBuilderValidators.compose([
            FormBuilderValidators.required(),
          ]),
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            labelText: 'Réponse',
          ),
        );

      case QuestionnaireResponseType.NUMBER:
        return FormBuilderTextField(
          name: 'response',
          initialValue: widget.editable?.response,
          keyboardType: TextInputType.number,
          autocorrect: false,
          enableSuggestions: false,
          validator: FormBuilderValidators.compose([
            FormBuilderValidators.required(),
            FormBuilderValidators.numeric(),
          ]),
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            labelText: 'Réponse',
          ),
        );

      case QuestionnaireResponseType.TEXT:
        return FormBuilderTextField(
          name: 'response',
          minLines: 2,
          maxLines: 10,
          initialValue: widget.editable?.response,
          keyboardType: TextInputType.multiline,
          autocorrect: true,
          enableSuggestions: true,
          textCapitalization: TextCapitalization.sentences,
          validator: FormBuilderValidators.required(),
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            labelText: 'Réponse',
          ),
        );
      // ignore: unreachable_switch_default
      default:
    }

    return const Text("Type non défini.");
  }
}
