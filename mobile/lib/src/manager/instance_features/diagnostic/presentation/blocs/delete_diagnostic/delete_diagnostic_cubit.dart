import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/domain/usecase/delete_diagnostic.dart';

part 'delete_diagnostic_state.dart';

class DeleteDiagnosticCubit extends Cubit<DeleteDiagnosticState> {
  final DeleteDiagnostic _deleteDiagnostic;
  final ConnectionChecker _connectionChecker;
  final String instanceId;

  DeleteDiagnosticCubit(
    this._deleteDiagnostic,
    this._connectionChecker, {
    required this.instanceId,
  }) : super(DeleteDiagnosticInitial());

  Future<void> deleteDiagnostic({required String diagnosticId}) async {
    emit(DeleteDiagnosticLoading());

    final isOnline = await _connectionChecker.isOnline();

    final result = await _deleteDiagnostic(DeleteDiagnosticParams(
      instanceId: instanceId,
      diagnosticId: diagnosticId,
    ));

    result.fold(
      (l) => emit(DeleteDiagnosticError(l.message)),
      (r) => emit(DeleteDiagnosticSuccess(
        message: r.message,
        isOffline: !isOnline,
      )),
    );
  }
}
