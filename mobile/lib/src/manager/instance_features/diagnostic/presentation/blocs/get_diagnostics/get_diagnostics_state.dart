part of 'get_diagnostics_cubit.dart';

@immutable
sealed class GetDiagnosticsState {}

final class GetDiagnosticsInitial extends GetDiagnosticsState {}

final class GetDiagnosticsLoading extends GetDiagnosticsState {}

final class GetDiagnosticsLoaded extends GetDiagnosticsState {
  final List<Diagnostic> diagnostics;
  final bool isOffline;

  GetDiagnosticsLoaded({required this.diagnostics, this.isOffline = false});
}

final class GetDiagnosticsError extends GetDiagnosticsState {
  final String error;

  GetDiagnosticsError(this.error);
}
