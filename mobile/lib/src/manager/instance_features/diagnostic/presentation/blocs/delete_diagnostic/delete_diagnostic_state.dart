part of 'delete_diagnostic_cubit.dart';

@immutable
sealed class DeleteDiagnosticState {}

final class DeleteDiagnosticInitial extends DeleteDiagnosticState {}

final class DeleteDiagnosticLoading extends DeleteDiagnosticState {}

final class DeleteDiagnosticSuccess extends DeleteDiagnosticState {
  final String message;
  final bool isOffline;

  DeleteDiagnosticSuccess({required this.message, this.isOffline = false});
}

final class DeleteDiagnosticError extends DeleteDiagnosticState {
  final String message;

  DeleteDiagnosticError(this.message);
}
