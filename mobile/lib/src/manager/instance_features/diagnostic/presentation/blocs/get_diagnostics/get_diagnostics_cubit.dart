import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:s3g/core/network/connection_checker.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/domain/entity/diagnostic.dart';

import '../../../domain/usecase/get_diagnostics.dart';

part 'get_diagnostics_state.dart';

class GetDiagnosticsCubit extends Cubit<GetDiagnosticsState> {
  final String instanceId;
  final GetDiagnostics _diagnostics;
  final ConnectionChecker _connectionChecker;

  GetDiagnosticsCubit(
    this._diagnostics,
    this._connectionChecker, {
    required this.instanceId,
  }) : super(GetDiagnosticsInitial());

  Future<void> getDiagnostics() async {
    emit(GetDiagnosticsLoading());

    final isOnline = await _connectionChecker.isOnline();

    final result = await _diagnostics(
      GetDiagnosticsParams(instanceId: instanceId),
    );

    result.fold(
      (failure) => emit(GetDiagnosticsError(failure.message)),
      (diagnostics) => emit(GetDiagnosticsLoaded(
        diagnostics: diagnostics,
        isOffline: !isOnline,
      )),
    );
  }
}
