import 'package:injectable/injectable.dart' hide Order;
import 'package:s3g/core/objectbox/objectbox.dart';
import 'package:s3g/src/manager/instance_features/diagnostic/data/local/models/diagnostic_local_model.dart';
import 'package:s3g/src/sync/sync_service.dart';

abstract class DiagnosticLocalDataSource {
  // CRUD operations
  Future<void> saveDiagnostic(DiagnosticLocalModel diagnostic);
  Future<void> saveDiagnostics(List<DiagnosticLocalModel> diagnostics);

  // UNIFIED ID lookup - handles both server ID and localId
  Future<DiagnosticLocalModel?> getDiagnosticById(String id);

  // Query operations
  Future<List<DiagnosticLocalModel>> getDiagnosticsByInstanceLocalId(
    String instanceLocalId,
  );

  Future<List<DiagnosticLocalModel>> searchDiagnostics(String searchTerm);

  // Content updates
  Future<void> updateDiagnosticContent(
    String id, {
    String? questionnaireId,
    String? response,
  });

  // Physical deletion methods - handles both server ID and localId
  Future<void> deleteDiagnostic(String id);

  // Soft deletion methods - handles both server ID and localId
  Future<void> markDiagnosticAsDeleted(String id);

  // Sync helper methods
  Future<void> markDiagnosticAsSynced(String localId, String serverId);
  Future<void> markDiagnosticSyncFailed(String localId, String error);
  Future<List<DiagnosticLocalModel>> getPendingSyncDiagnostics();
  Future<void> updateDiagnosticFromServer(
      DiagnosticLocalModel serverDiagnostic);
}

@Injectable(as: DiagnosticLocalDataSource)
class DiagnosticLocalDataSourceImpl implements DiagnosticLocalDataSource {
  final ObjectBox objectBox;

  DiagnosticLocalDataSourceImpl({required this.objectBox});

  Box<DiagnosticLocalModel> get _box =>
      objectBox.store.box<DiagnosticLocalModel>();

  @override
  Future<void> saveDiagnostic(DiagnosticLocalModel diagnostic) async {
    DiagnosticLocalModel? existing;

    // Use the unified getDiagnosticById method
    if (diagnostic.id != null) {
      existing = await getDiagnosticById(diagnostic.id!);
    }

    // If not found by server ID, check by localId
    existing ??= await getDiagnosticById(diagnostic.localId);

    // Check if diagnostic with questionnaireId and instanceLocalId already exists
    if (existing == null) {
      final queryByTypeAndInstance = _box
          .query(
            DiagnosticLocalModel_.questionnaireId
                    .equals(diagnostic.questionnaireId) &
                DiagnosticLocalModel_.instanceLocalId
                    .equals(diagnostic.instanceLocalId),
          )
          .build();

      existing = await queryByTypeAndInstance.findFirstAsync();
      queryByTypeAndInstance.close();
    }

    if (existing != null) {
      // Update existing record, preserving ObjectBox ID
      existing.id = diagnostic.id; // Update server ID if assigned
      existing.questionnaireId = diagnostic.questionnaireId;
      existing.response = diagnostic.response;
      existing.syncStatus = diagnostic.syncStatus;
      existing.lastSyncAttempt = diagnostic.lastSyncAttempt;
      existing.syncError = diagnostic.syncError;
      existing.isDeleted = diagnostic.isDeleted;
      existing.updatedAt = diagnostic.updatedAt;
      await _box.putAsync(existing);
      return;
    }

    // No existing record found, save as new
    await _box.putAsync(diagnostic);
  }

  @override
  Future<void> saveDiagnostics(List<DiagnosticLocalModel> diagnostics) async {
    // Process each diagnostic individually to handle updates
    for (final diagnostic in diagnostics) {
      await saveDiagnostic(diagnostic);
    }
  }

  @override
  Future<DiagnosticLocalModel?> getDiagnosticById(String id) async {
    final query = _box
        .query(DiagnosticLocalModel_.id.equals(id) |
            DiagnosticLocalModel_.localId.equals(id))
        .build();
    final result = await query.findFirstAsync();
    query.close();
    return result;
  }

  @override
  Future<List<DiagnosticLocalModel>> getDiagnosticsByInstanceLocalId(
      String instanceLocalId) async {
    final query = _box
        .query(DiagnosticLocalModel_.instanceLocalId.equals(instanceLocalId) &
            DiagnosticLocalModel_.isDeleted.equals(false))
        .order(DiagnosticLocalModel_.createdAt, flags: Order.descending)
        .build();
    final results = await query.findAsync();
    query.close();
    return results;
  }

  @override
  Future<List<DiagnosticLocalModel>> searchDiagnostics(
      String searchTerm) async {
    final query = _box
        .query(DiagnosticLocalModel_.response.contains(searchTerm) &
            DiagnosticLocalModel_.isDeleted.equals(false))
        .order(DiagnosticLocalModel_.createdAt, flags: Order.descending)
        .build();
    final results = await query.findAsync();
    query.close();
    return results;
  }

  @override
  Future<void> updateDiagnosticContent(
    String id, {
    String? questionnaireId,
    String? response,
  }) async {
    final diagnostic = await getDiagnosticById(id);
    if (diagnostic == null) return;

    diagnostic.updateContent(
      newQuestionnaireId: questionnaireId,
      newResponse: response,
    );

    await saveDiagnostic(diagnostic);
  }

  @override
  Future<void> deleteDiagnostic(String id) async {
    final diagnostic = await getDiagnosticById(id);
    if (diagnostic != null) {
      await _box.removeAsync(diagnostic.oid);
    }
  }

  @override
  Future<void> markDiagnosticAsDeleted(String id) async {
    final diagnostic = await getDiagnosticById(id);
    if (diagnostic == null) return;

    diagnostic.markAsDeleted();
    await saveDiagnostic(diagnostic);
  }

  @override
  Future<void> markDiagnosticAsSynced(String localId, String serverId) async {
    final diagnostic = await getDiagnosticById(localId);
    if (diagnostic == null) return;

    diagnostic.markAsSynced(serverId);
    await saveDiagnostic(diagnostic);
  }

  @override
  Future<void> markDiagnosticSyncFailed(String localId, String error) async {
    final diagnostic = await getDiagnosticById(localId);
    if (diagnostic == null) return;

    diagnostic.updateSyncStatus(SyncStatus.failed, error: error);
    await saveDiagnostic(diagnostic);
  }

  @override
  Future<List<DiagnosticLocalModel>> getPendingSyncDiagnostics() async {
    // CRITICAL: Do NOT filter out deleted items!
    // Deleted items MUST sync to inform server about deletion
    final query = _box
        .query(DiagnosticLocalModel_.syncStatus
                .equals(SyncStatus.pending.value) |
            DiagnosticLocalModel_.syncStatus.equals(SyncStatus.failed.value))
        .build();
    final results = await query.findAsync();
    query.close();
    return results;
  }

  @override
  Future<void> updateDiagnosticFromServer(
      DiagnosticLocalModel serverDiagnostic) async {
    final existing = await getDiagnosticById(serverDiagnostic.id!);
    if (existing != null) {
      // Update with server data
      existing.questionnaireId = serverDiagnostic.questionnaireId;
      existing.response = serverDiagnostic.response;
      existing.syncStatus = SyncStatus.synced.value;
      existing.lastSyncAttempt = DateTime.now();
      existing.syncError = null;
      existing.updatedAt = serverDiagnostic.updatedAt;
      await saveDiagnostic(existing);
    } else {
      // Save new from server
      serverDiagnostic.syncStatus = SyncStatus.synced.value;
      await saveDiagnostic(serverDiagnostic);
    }
  }
}
