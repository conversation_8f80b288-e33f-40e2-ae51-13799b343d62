import 'dart:developer';

import 'package:fpdart/fpdart.dart';
import 'package:injectable/injectable.dart';
import 'package:s3g/core/errors/failures.dart';
import 'package:s3g/core/helpers/request_helper.dart';
import 'package:s3g/core/http/response.dart';
import 'package:s3g/core/repository/repository.dart';
import 'package:s3g/core/network/connection_checker.dart';

import 'package:s3g/src/sync/sync_service.dart';

import '../../domain/entity/relapse.dart' show Relapse;
import '../../domain/repository/relapse_repository.dart';
import '../remote/models/relapse_model.dart' show RelapseModel;
import '../remote/relapse_remote_datasource.dart';
import '../local/relapse_local_datasource.dart';
import '../local/models/relapse_local_model.dart';
import 'package:s3g/src/manager/instance/data/local/instance_local_datasource.dart';
import 'package:s3g/src/manager/instance/data/local/models/instance_local_model.dart';

@Injectable(as: RelapseRepository)
class RelapseRepositoryImpl implements RelapseRepository {
  final RelapseRemoteDataSource _relapseRemoteDataSource;
  final InstanceRelapseLocalDataSource _localDataSource;
  final InstanceLocalDataSource _instanceLocalDataSource;
  final ConnectionChecker _connectionChecker;
  final SyncService _syncService;

  RelapseRepositoryImpl(
    this._relapseRemoteDataSource,
    this._localDataSource,
    this._instanceLocalDataSource,
    this._connectionChecker,
    this._syncService,
  );

  // Helper method to get instance by ID or localId
  Future<InstanceLocalModel?> _getInstanceByIdOrLocalId(
    String instanceId,
  ) async {
    // Try by server ID first
    var instance = await _instanceLocalDataSource.getInstanceById(instanceId);
    return instance;
  }

  // Helper method to get relapse by instance ID (converts to instanceLocalId)
  Future<InstanceRelapseLocalModel?> _getRelapseByInstanceId(
    String instanceId,
  ) async {
    final instance = await _getInstanceByIdOrLocalId(instanceId);
    if (instance == null) return null;
    return await _localDataSource.getRelapseByInstanceLocalId(instance.localId);
  }

  @override
  RepositoryResponse<Relapse> createRelapse({
    required String instanceId,
    required String description,
  }) async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // Try to create on server first
        final response = await requestHelper(
          () => _relapseRemoteDataSource.createRelapse(
            instanceId: instanceId,
            description: description,
          ),
        );

        if (response.isRight()) {
          final relapse = response.getOrElse((l) => throw l);

          final instance = await _getInstanceByIdOrLocalId(instanceId);
          if (instance != null) {
            // Save to local storage as synced
            final localRelapse = InstanceRelapseLocalModel.fromEntity(
              relapse,
              instanceLocalId: instance.localId,
            );

            _localDataSource.saveRelapse(localRelapse);
          }
        }

        return response;
      } else {
        // Create offline - get instance localId
        final instance = await _getInstanceByIdOrLocalId(instanceId);
        if (instance == null) {
          return Left(CacheFailure('Cas introuvable'));
        }

        final localRelapse = InstanceRelapseLocalModel.createOffline(
          instanceLocalId: instance.localId,
          description: description,
        );

        // Save to local storage
        await _localDataSource.saveRelapse(localRelapse);

        // Add relapse to instance
        final relapseModel = RelapseModel.fromEntity(localRelapse.toEntity());
        await _instanceLocalDataSource.addRelapseToInstance(
          instanceId,
          relapseModel,
        );

        return Right(localRelapse.toEntity());
      }
    } catch (e) {
      log('Error creating relapse: $e');
      return Left(DatabaseFailure('Erreur lors de la création de la rechute'));
    }
  }

  @override
  RepositoryResponse<MessageResponse> deleteRelapse({
    required String instanceId,
  }) async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // Try to delete on server first
        final response = await requestHelper(
          () => _relapseRemoteDataSource.deleteRelapse(
            instanceId: instanceId,
          ),
        );

        if (response.isRight()) {
          // Delete from local storage
          final existingRelapse = await _getRelapseByInstanceId(instanceId);

          if (existingRelapse != null) {
            _localDataSource.deleteRelapse(existingRelapse.localId);
          }
        }

        return response;
      } else {
        // Mark as deleted offline
        final existingRelapse = await _getRelapseByInstanceId(instanceId);

        if (existingRelapse != null) {
          await _localDataSource.markRelapseAsDeleted(existingRelapse.localId);

          // Remove relapse from instance
          await _instanceLocalDataSource.removeRelapseFromInstance(instanceId);

          return Right(
            MessageResponse(message: 'Rechute supprimée hors ligne'),
          );
        } else {
          return Left(
            CacheFailure('Rechute introuvable pour suppression hors ligne'),
          );
        }
      }
    } catch (e) {
      log('Error deleting relapse: $e');
      return Left(
        DatabaseFailure('Erreur lors de la suppression de la rechute'),
      );
    }
  }

  @override
  RepositoryResponse<Relapse> getRelapse({required String instanceId}) async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // First, sync any pending local changes to avoid overwriting them
        try {
          await _syncService.syncManagerRelapses();
        } catch (e) {
          log('Warning: Failed to sync pending relapses before fetch: $e');
          // Continue with fetch even if sync fails
        }

        // Try to get from server first
        final response = await requestHelper(
          () => _relapseRemoteDataSource.getRelapse(instanceId: instanceId),
        );

        if (response.isRight()) {
          final relapse = response.getOrElse((l) => throw l);

          final instance = await _getInstanceByIdOrLocalId(instanceId);
          if (instance != null) {
            // Save to local storage as synced
            final localRelapse = InstanceRelapseLocalModel.fromEntity(
              relapse,
              instanceLocalId: instance.localId,
            );

            _localDataSource.saveRelapse(localRelapse);
          }
        }

        return response;
      } else {
        // Sometime the relapse is not in the local storage but the companion has
        // a relapse in its instance. In that case, we need to create a local
        // relapse from the companion instance.
        final localRelapse = await _getRelapseByInstanceId(instanceId);

        final instance = await _getInstanceByIdOrLocalId(instanceId);

        if (localRelapse == null && instance != null) {
          final instanceEntity = instance.toEntity();
          final relapse = instanceEntity.relapse;

          if (relapse != null) {
            final localModel = InstanceRelapseLocalModel.fromEntity(
              relapse,
              instanceLocalId: instance.localId,
            );

            await _localDataSource.saveRelapse(localModel);
            return Right(relapse);
          }
        }

        if (localRelapse != null && !localRelapse.isDeleted) {
          return Right(localRelapse.toEntity());
        }

        return Left(CacheFailure('Rechute introuvable dans le stockage local'));
      }
    } catch (e) {
      log('Error getting relapse: $e');
      return Left(
        DatabaseFailure('Erreur lors de la récupération de la rechute'),
      );
    }
  }

  @override
  RepositoryResponse<Relapse> updateRelapse({
    required String instanceId,
    required String description,
  }) async {
    try {
      // Check if device is online
      final isOnline = await _connectionChecker.isOnline();

      if (isOnline) {
        // Try to update on server first
        final response =
            await requestHelper(() => _relapseRemoteDataSource.updateRelapse(
                  instanceId: instanceId,
                  description: description,
                ));

        return response.fold(
          (failure) => Left(failure),
          (relapse) async {
            // Get instance to get its localId
            final instance = await _getInstanceByIdOrLocalId(instanceId);
            if (instance != null) {
              // Update local storage as synced
              final localRelapse = InstanceRelapseLocalModel.fromEntity(
                relapse,
                instanceLocalId: instance.localId,
              );
              await _localDataSource.saveRelapse(localRelapse);
            }
            return Right(relapse);
          },
        );
      } else {
        // Update offline
        final existingRelapse = await _getRelapseByInstanceId(instanceId);
        if (existingRelapse != null) {
          await _localDataSource.updateRelapseContent(
            existingRelapse.localId,
            description: description,
          );

          final updatedRelapse =
              await _localDataSource.getRelapseById(existingRelapse.localId);

          return Right(updatedRelapse!.toEntity());
        } else {
          return Left(
              CacheFailure('Rechute introuvable pour modification hors ligne'));
        }
      }
    } catch (e) {
      log('Error updating relapse: $e');
      return Left(
          DatabaseFailure('Erreur lors de la modification de la rechute'));
    }
  }
}
