import 'dart:io';
import 'package:flutter/material.dart';
import 'package:getwidget/getwidget.dart';
import 'package:s3g/common/common.dart';
import 'package:s3g/core/container/injectable.dart';
import 'package:s3g/core/helpers/url.dart';
import 'package:s3g/core/utils/attachment_storage_service.dart';

class AttachmentViewer extends StatelessWidget {
  final String? attachment;
  final bool onDetailPage;

  const AttachmentViewer({
    super.key,
    required this.attachment,
    this.onDetailPage = false,
  });

  @override
  Widget build(BuildContext context) {
    if (attachment == null) {
      return const SizedBox.shrink();
    }

    final attachmentStorage = getIt<AttachmentStorageService>();

    // Check if it's not a local attachment
    if (!attachmentStorage.isOfflineAttachment(attachment!)) {
      final remoteUrl = remoteStorageFile(attachment!);

      if (onDetailPage) {
        return FileViewer(
          url: remoteUrl,
          loaderWidget: const Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              GFLoader(),
              Text('Pièce jointe'),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(40),
                ),
                child: const Icon(Icons.attach_file),
              ),
              const Text('Pièce jointe'),
            ],
          ),
        );
      }

      return FileViewer(url: remoteUrl);
    }

    // It's a local file, we need to get it from storage
    return FutureBuilder<File?>(
      future: attachmentStorage.getAttachment(attachment!),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const GFLoader();
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return const SizedBox.shrink();
        }

        final file = snapshot.data!;

        return FileViewer(url: file.path);
      },
    );
  }
}
