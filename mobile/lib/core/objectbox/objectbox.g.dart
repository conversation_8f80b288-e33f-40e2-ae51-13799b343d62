// GENERATED CODE - DO NOT MODIFY BY HAND
// This code was generated by ObjectBox. To update it run the generator again
// with `dart run build_runner build`.
// See also https://docs.objectbox.io/getting-started#generate-objectbox-code

// ignore_for_file: camel_case_types, depend_on_referenced_packages
// coverage:ignore-file

import 'dart:typed_data';

import 'package:flat_buffers/flat_buffers.dart' as fb;
import 'package:objectbox/internal.dart'
    as obx_int; // generated code can access "internal" functionality
import 'package:objectbox/objectbox.dart' as obx;
import 'package:objectbox_flutter_libs/objectbox_flutter_libs.dart';

import '../../src/authentication/data/local/models/user_local_model.dart';
import '../../src/companion/companion/data/local/models/companion_local_model.dart';
import '../../src/companion/followup/data/local/models/followup_local_model.dart';
import '../../src/companion/relapse/data/local/models/relapse_local_model.dart';
import '../../src/manager/health_center/data/local/models/health_center_local_model.dart';
import '../../src/manager/health_center/data/local/models/health_zone_local_model.dart';
import '../../src/manager/instance/data/local/models/instance_local_model.dart';
import '../../src/manager/instance_features/companion/data/local/models/instance_companion_local_model.dart';
import '../../src/manager/instance_features/diagnostic/data/local/models/diagnostic_local_model.dart';
import '../../src/manager/instance_features/relapse/data/local/models/relapse_local_model.dart';
import '../../src/manager/instance_features/treatment/data/local/models/treatment_local_model.dart';
import '../../src/manager/member/data/local/models/member_local_model.dart';
import '../../src/manager/questionnaire/data/local/models/questionnaire_local_model.dart';

export 'package:objectbox/objectbox.dart'; // so that callers only have to import this file

final _entities = <obx_int.ModelEntity>[
  obx_int.ModelEntity(
    id: const obx_int.IdUid(1, 4843187991762130878),
    name: 'UserLocalModel',
    lastPropertyId: const obx_int.IdUid(11, 8755141269492670503),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 8150247937050990135),
        name: 'id',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(1, 6696137614632170115),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 4867154126574018817),
        name: 'name',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 7808261147011250497),
        name: 'role',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 5861495493908320549),
        name: 'phone',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 4256530412587493788),
        name: 'email',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 4494853835445859077),
        name: 'description',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 330474981545105513),
        name: 'createdAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 5940803811252183204),
        name: 'updatedAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 5089396876425374590),
        name: 'oid',
        type: 6,
        flags: 1,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(2, 123456789012345678),
    name: 'InstanceLocalModel',
    lastPropertyId: const obx_int.IdUid(20, 3199587564168808968),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 111111111111111111),
        name: 'oid',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 222222222222222222),
        name: 'id',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(2, 222222222222222223),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 333333333333333333),
        name: 'code',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 444444444444444444),
        name: 'type',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 555555555555555555),
        name: 'status',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 666666666666666666),
        name: 'survivorCode',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 777777777777777777),
        name: 'healthCenterId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 888888888888888888),
        name: 'description',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 999999999999999999),
        name: 'createdAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 101010101010101010),
        name: 'updatedAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 111111111111111112),
        name: 'syncStatus',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(12, 121212121212121212),
        name: 'localId',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(3, 333333333333333334),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(13, 131313131313131313),
        name: 'lastSyncAttempt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(14, 141414141414141414),
        name: 'syncError',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(15, 151515151515151515),
        name: 'isDeleted',
        type: 1,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(16, 2067695744753375725),
        name: 'relapsedAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(17, 8917855125211569473),
        name: 'survivorJson',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(18, 1337283031199994753),
        name: 'healthCenterJson',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(19, 3718597701250203606),
        name: 'relapseJson',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(20, 3199587564168808968),
        name: 'creatorJson',
        type: 9,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(3, 234567890123456789),
    name: 'FollowupLocalModel',
    lastPropertyId: const obx_int.IdUid(13, 213131313131313131),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 211111111111111111),
        name: 'oid',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 222222222222222224),
        name: 'id',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(4, 444444444444444445),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 233333333333333333),
        name: 'title',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 244444444444444444),
        name: 'description',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 255555555555555555),
        name: 'companionId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 266666666666666666),
        name: 'instanceId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 277777777777777777),
        name: 'createdAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 288888888888888888),
        name: 'updatedAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 299999999999999999),
        name: 'syncStatus',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 210101010101010101),
        name: 'localId',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(5, 555555555555555556),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 211111111111111113),
        name: 'lastSyncAttempt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(12, 212121212121212121),
        name: 'syncError',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(13, 213131313131313131),
        name: 'isDeleted',
        type: 1,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(5, 2131990075469944008),
    name: 'CompanionLocalModel',
    lastPropertyId: const obx_int.IdUid(12, 5353034930472726044),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 5486570673802985772),
        name: 'oid',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 5994209660998436109),
        name: 'id',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(6, 7931814194615750109),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 1941241111482350407),
        name: 'type',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 5260299708132266012),
        name: 'instanceId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 5684648266782974248),
        name: 'createdAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 7907347413778994749),
        name: 'updatedAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 7796806207075825512),
        name: 'localId',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(7, 7079359060673993916),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 6913043116634890509),
        name: 'isDeleted',
        type: 1,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(12, 5353034930472726044),
        name: 'instanceJson',
        type: 9,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(7, 1206948962258459754),
    name: 'HealthCenterLocalModel',
    lastPropertyId: const obx_int.IdUid(15, 6523415319098896387),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 3965859350121517998),
        name: 'oid',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 8910810775081782794),
        name: 'id',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(10, 3224088783981629108),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 3811263425641669684),
        name: 'name',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 8889649924848815357),
        name: 'address',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 3155341395934483177),
        name: 'phone',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 3328532811955737804),
        name: 'servicesOffered',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 5311081021285726220),
        name: 'aps',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 1327041396974672556),
        name: 'companions',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 4030775453842690757),
        name: 'instances',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 4334806944805334056),
        name: 'relapses',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 633010979576058339),
        name: 'responsibility',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(12, 1570670106292094135),
        name: 'healthZoneJson',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(13, 4585350839257487354),
        name: 'createdAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(14, 65781006731592663),
        name: 'updatedAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(15, 6523415319098896387),
        name: 'lastSyncedAt',
        type: 10,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(8, 5680527812754074453),
    name: 'HealthZoneLocalModel',
    lastPropertyId: const obx_int.IdUid(6, 3248450387396194659),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 9099099414556106493),
        name: 'oid',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 2252304006184629140),
        name: 'id',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(11, 5768413447315666547),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 1111439678387543468),
        name: 'name',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 6531230281514491711),
        name: 'populationServed',
        type: 6,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 2991545496507772860),
        name: 'createdAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 3248450387396194659),
        name: 'updatedAt',
        type: 10,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(9, 3977560403868452195),
    name: 'CompanionRelapseLocalModel',
    lastPropertyId: const obx_int.IdUid(11, 3332868420746362183),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 5700778090457230225),
        name: 'oid',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 4395371202231324200),
        name: 'id',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(20, 3547353703867431408),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 1544457817321428622),
        name: 'localId',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(21, 8963891790248699014),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 151088788059340459),
        name: 'companionId',
        type: 9,
        flags: 2048,
        indexId: const obx_int.IdUid(22, 5658318110842828008),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 7779561520767444938),
        name: 'description',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 9045273673877848939),
        name: 'syncStatus',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 5676178513798200874),
        name: 'lastSyncAttempt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 2968322266771921268),
        name: 'syncError',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 5559514721541433691),
        name: 'isDeleted',
        type: 1,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 3972545048896093880),
        name: 'createdAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 3332868420746362183),
        name: 'updatedAt',
        type: 10,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(10, 6809228688357094652),
    name: 'InstanceRelapseLocalModel',
    lastPropertyId: const obx_int.IdUid(12, 8067655157872396733),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 8636559884267916027),
        name: 'oid',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 9221868290604143232),
        name: 'id',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(23, 5775276047666512758),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 7001970905617038198),
        name: 'localId',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(24, 7727731150027515677),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 8829017520108300061),
        name: 'description',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 4718181757980925303),
        name: 'syncStatus',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 7426645132041487092),
        name: 'lastSyncAttempt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 6527944039596823871),
        name: 'syncError',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 3631081375662263007),
        name: 'isDeleted',
        type: 1,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 4870987455641611355),
        name: 'createdAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 3280701053125573813),
        name: 'updatedAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(12, 8067655157872396733),
        name: 'instanceLocalId',
        type: 9,
        flags: 2048,
        indexId: const obx_int.IdUid(26, 5367647898160623552),
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(11, 3164507662099933400),
    name: 'InstanceCompanionLocalModel',
    lastPropertyId: const obx_int.IdUid(12, 355905144806822505),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 3763901172611099787),
        name: 'oid',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 6866793507894497094),
        name: 'id',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(12, 1568671972986800250),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 2022603295548964827),
        name: 'localId',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(13, 6060253294180437095),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 7035070993017972520),
        name: 'instanceLocalId',
        type: 9,
        flags: 2048,
        indexId: const obx_int.IdUid(14, 8540622414885268636),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 1042704430004654935),
        name: 'type',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 7641415271262807680),
        name: 'userJson',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 1523234666763861504),
        name: 'syncStatus',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 6706543861244143601),
        name: 'lastSyncAttempt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 7057319490283205365),
        name: 'syncError',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 8160391148219200306),
        name: 'isDeleted',
        type: 1,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 2309829828985691886),
        name: 'createdAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(12, 355905144806822505),
        name: 'updatedAt',
        type: 10,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(12, 2822108779830572564),
    name: 'MemberLocalModel',
    lastPropertyId: const obx_int.IdUid(12, 6803541314689736033),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 1234962094324276160),
        name: 'oid',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 4925758493464560538),
        name: 'id',
        type: 9,
        flags: 2048,
        indexId: const obx_int.IdUid(15, 4162002983318571608),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 2981032850090524115),
        name: 'name',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 3689744126947909949),
        name: 'email',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 2991002803398878388),
        name: 'role',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 14672183895157632),
        name: 'responsibility',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 1008441726396594711),
        name: 'companionRole',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 148444596010105156),
        name: 'phone',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 3063610174526907481),
        name: 'description',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 3727997205887903228),
        name: 'createdAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 5660630452644298254),
        name: 'updatedAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(12, 6803541314689736033),
        name: 'healthCenterId',
        type: 9,
        flags: 2048,
        indexId: const obx_int.IdUid(19, 3406569888483827386),
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(14, 8930856652812031345),
    name: 'QuestionnaireLocalModel',
    lastPropertyId: const obx_int.IdUid(8, 7401302589380796055),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 277974317547909251),
        name: 'oid',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 44398720361841798),
        name: 'id',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(18, 9196782966579820819),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 5442426107910623341),
        name: 'question',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 3920658988480017100),
        name: 'type',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 9094428199092741294),
        name: 'hint',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 6555616267116442702),
        name: 'createdAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 2965190484639975832),
        name: 'updatedAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 7401302589380796055),
        name: 'choicesJson',
        type: 9,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(15, 794324914771890790),
    name: 'TreatmentLocalModel',
    lastPropertyId: const obx_int.IdUid(13, 443301228124551133),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 2231260207304713079),
        name: 'oid',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 7127836358160527791),
        name: 'id',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(27, 5437840758081006804),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 4911385562647085571),
        name: 'localId',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(28, 8895191980031646954),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 6868436804111835817),
        name: 'instanceLocalId',
        type: 9,
        flags: 2048,
        indexId: const obx_int.IdUid(29, 5786279990472633264),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 3212678726846352828),
        name: 'type',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 5578572457757629787),
        name: 'observation',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 8377757730913963314),
        name: 'attachment',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 4501441105217128628),
        name: 'syncStatus',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 1433475352703916422),
        name: 'lastSyncAttempt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 8030138906235264810),
        name: 'syncError',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 7166655529622965777),
        name: 'isDeleted',
        type: 1,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(12, 2442877933944517188),
        name: 'createdAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(13, 443301228124551133),
        name: 'updatedAt',
        type: 10,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
  obx_int.ModelEntity(
    id: const obx_int.IdUid(16, 1787216815381049273),
    name: 'DiagnosticLocalModel',
    lastPropertyId: const obx_int.IdUid(12, 2564953454279929270),
    flags: 0,
    properties: <obx_int.ModelProperty>[
      obx_int.ModelProperty(
        id: const obx_int.IdUid(1, 2637034690065199846),
        name: 'oid',
        type: 6,
        flags: 1,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(2, 4666337916405435503),
        name: 'id',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(30, 4295818981976953059),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(3, 7338184946652564647),
        name: 'localId',
        type: 9,
        flags: 2080,
        indexId: const obx_int.IdUid(31, 8885512626812229207),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(4, 2711339811238821312),
        name: 'instanceLocalId',
        type: 9,
        flags: 2048,
        indexId: const obx_int.IdUid(32, 6096184751262859856),
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(5, 1110634529768559342),
        name: 'questionnaireId',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(6, 3315300823302868145),
        name: 'response',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(7, 5907925552307193602),
        name: 'syncStatus',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(8, 1742071970354330874),
        name: 'lastSyncAttempt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(9, 8177632234021446112),
        name: 'syncError',
        type: 9,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(10, 8139882313854545307),
        name: 'isDeleted',
        type: 1,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(11, 7709993981503300151),
        name: 'createdAt',
        type: 10,
        flags: 0,
      ),
      obx_int.ModelProperty(
        id: const obx_int.IdUid(12, 2564953454279929270),
        name: 'updatedAt',
        type: 10,
        flags: 0,
      ),
    ],
    relations: <obx_int.ModelRelation>[],
    backlinks: <obx_int.ModelBacklink>[],
  ),
];

/// Shortcut for [obx.Store.new] that passes [getObjectBoxModel] and for Flutter
/// apps by default a [directory] using `defaultStoreDirectory()` from the
/// ObjectBox Flutter library.
///
/// Note: for desktop apps it is recommended to specify a unique [directory].
///
/// See [obx.Store.new] for an explanation of all parameters.
///
/// For Flutter apps, also calls `loadObjectBoxLibraryAndroidCompat()` from
/// the ObjectBox Flutter library to fix loading the native ObjectBox library
/// on Android 6 and older.
Future<obx.Store> openStore({
  String? directory,
  int? maxDBSizeInKB,
  int? maxDataSizeInKB,
  int? fileMode,
  int? maxReaders,
  bool queriesCaseSensitiveDefault = true,
  String? macosApplicationGroup,
}) async {
  await loadObjectBoxLibraryAndroidCompat();
  return obx.Store(
    getObjectBoxModel(),
    directory: directory ?? (await defaultStoreDirectory()).path,
    maxDBSizeInKB: maxDBSizeInKB,
    maxDataSizeInKB: maxDataSizeInKB,
    fileMode: fileMode,
    maxReaders: maxReaders,
    queriesCaseSensitiveDefault: queriesCaseSensitiveDefault,
    macosApplicationGroup: macosApplicationGroup,
  );
}

/// Returns the ObjectBox model definition for this project for use with
/// [obx.Store.new].
obx_int.ModelDefinition getObjectBoxModel() {
  final model = obx_int.ModelInfo(
    entities: _entities,
    lastEntityId: const obx_int.IdUid(16, 1787216815381049273),
    lastIndexId: const obx_int.IdUid(32, 6096184751262859856),
    lastRelationId: const obx_int.IdUid(0, 0),
    lastSequenceId: const obx_int.IdUid(0, 0),
    retiredEntityUids: const [
      7881812106990892811,
      1945190895619663006,
      1384164074911263481,
    ],
    retiredIndexUids: const [826561280328193907],
    retiredPropertyUids: const [
      2732646462392475608,
      8755141269492670503,
      4879820946144288907,
      8272258262473875575,
      6581317629370286128,
      5606941273233180838,
      1302224599751260177,
      3454574729843236120,
      5189089791841038012,
      832552586866868912,
      3521151883837436064,
      8340965384149234714,
      7180449859713486640,
      1052031254800829878,
      3304613512950611961,
      4770782705386923279,
      5649284891491276068,
      5737624744565037624,
      5705483735001706751,
      8262436783262865713,
      4827601966653328459,
      7917887324526627988,
      3646374292155004746,
      2959536292104268861,
      7036360107462867196,
      6309937417859282637,
      7626130646663799528,
      3401376047370408258,
      2216431904208992702,
      986515967099688258,
      313681023087369864,
      1735192707651803392,
      8108515293488047994,
      1643234975298268111,
      718983673961206878,
      7795727801655358653,
    ],
    retiredRelationUids: const [],
    modelVersion: 5,
    modelVersionParserMinimum: 5,
    version: 1,
  );

  final bindings = <Type, obx_int.EntityDefinition>{
    UserLocalModel: obx_int.EntityDefinition<UserLocalModel>(
      model: _entities[0],
      toOneRelations: (UserLocalModel object) => [],
      toManyRelations: (UserLocalModel object) => {},
      getId: (UserLocalModel object) => object.oid,
      setId: (UserLocalModel object, int id) {
        object.oid = id;
      },
      objectToFB: (UserLocalModel object, fb.Builder fbb) {
        final idOffset = fbb.writeString(object.id);
        final nameOffset = fbb.writeString(object.name);
        final roleOffset = fbb.writeString(object.role);
        final phoneOffset = fbb.writeString(object.phone);
        final emailOffset = object.email == null
            ? null
            : fbb.writeString(object.email!);
        final descriptionOffset = object.description == null
            ? null
            : fbb.writeString(object.description!);
        fbb.startTable(12);
        fbb.addOffset(1, idOffset);
        fbb.addOffset(2, nameOffset);
        fbb.addOffset(3, roleOffset);
        fbb.addOffset(4, phoneOffset);
        fbb.addOffset(5, emailOffset);
        fbb.addOffset(6, descriptionOffset);
        fbb.addInt64(7, object.createdAt.millisecondsSinceEpoch);
        fbb.addInt64(8, object.updatedAt.millisecondsSinceEpoch);
        fbb.addInt64(9, object.oid);
        fbb.finish(fbb.endTable());
        return object.oid;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final oidParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          22,
          0,
        );
        final idParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final nameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final roleParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final phoneParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 12, '');
        final emailParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 14);
        final descriptionParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 16);
        final createdAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 18, 0),
        );
        final updatedAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 20, 0),
        );
        final object = UserLocalModel(
          oid: oidParam,
          id: idParam,
          name: nameParam,
          role: roleParam,
          phone: phoneParam,
          email: emailParam,
          description: descriptionParam,
          createdAt: createdAtParam,
          updatedAt: updatedAtParam,
        );

        return object;
      },
    ),
    InstanceLocalModel: obx_int.EntityDefinition<InstanceLocalModel>(
      model: _entities[1],
      toOneRelations: (InstanceLocalModel object) => [],
      toManyRelations: (InstanceLocalModel object) => {},
      getId: (InstanceLocalModel object) => object.oid,
      setId: (InstanceLocalModel object, int id) {
        object.oid = id;
      },
      objectToFB: (InstanceLocalModel object, fb.Builder fbb) {
        final idOffset = object.id == null ? null : fbb.writeString(object.id!);
        final codeOffset = fbb.writeString(object.code);
        final typeOffset = fbb.writeString(object.type);
        final statusOffset = fbb.writeString(object.status);
        final survivorCodeOffset = fbb.writeString(object.survivorCode);
        final healthCenterIdOffset = fbb.writeString(object.healthCenterId);
        final descriptionOffset = object.description == null
            ? null
            : fbb.writeString(object.description!);
        final syncStatusOffset = fbb.writeString(object.syncStatus);
        final localIdOffset = fbb.writeString(object.localId);
        final syncErrorOffset = object.syncError == null
            ? null
            : fbb.writeString(object.syncError!);
        final survivorJsonOffset = object.survivorJson == null
            ? null
            : fbb.writeString(object.survivorJson!);
        final healthCenterJsonOffset = object.healthCenterJson == null
            ? null
            : fbb.writeString(object.healthCenterJson!);
        final relapseJsonOffset = object.relapseJson == null
            ? null
            : fbb.writeString(object.relapseJson!);
        final creatorJsonOffset = object.creatorJson == null
            ? null
            : fbb.writeString(object.creatorJson!);
        fbb.startTable(21);
        fbb.addInt64(0, object.oid);
        fbb.addOffset(1, idOffset);
        fbb.addOffset(2, codeOffset);
        fbb.addOffset(3, typeOffset);
        fbb.addOffset(4, statusOffset);
        fbb.addOffset(5, survivorCodeOffset);
        fbb.addOffset(6, healthCenterIdOffset);
        fbb.addOffset(7, descriptionOffset);
        fbb.addInt64(8, object.createdAt.millisecondsSinceEpoch);
        fbb.addInt64(9, object.updatedAt.millisecondsSinceEpoch);
        fbb.addOffset(10, syncStatusOffset);
        fbb.addOffset(11, localIdOffset);
        fbb.addInt64(12, object.lastSyncAttempt?.millisecondsSinceEpoch);
        fbb.addOffset(13, syncErrorOffset);
        fbb.addBool(14, object.isDeleted);
        fbb.addInt64(15, object.relapsedAt?.millisecondsSinceEpoch);
        fbb.addOffset(16, survivorJsonOffset);
        fbb.addOffset(17, healthCenterJsonOffset);
        fbb.addOffset(18, relapseJsonOffset);
        fbb.addOffset(19, creatorJsonOffset);
        fbb.finish(fbb.endTable());
        return object.oid;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final lastSyncAttemptValue = const fb.Int64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          28,
        );
        final relapsedAtValue = const fb.Int64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          34,
        );
        final oidParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final idParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 6);
        final codeParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final typeParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final statusParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 12, '');
        final survivorCodeParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 14, '');
        final healthCenterIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 16, '');
        final localIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 26, '');
        final descriptionParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 18);
        final createdAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 20, 0),
        );
        final updatedAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 22, 0),
        );
        final syncStatusParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 24, '');
        final lastSyncAttemptParam = lastSyncAttemptValue == null
            ? null
            : DateTime.fromMillisecondsSinceEpoch(lastSyncAttemptValue);
        final syncErrorParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 30);
        final isDeletedParam = const fb.BoolReader().vTableGet(
          buffer,
          rootOffset,
          32,
          false,
        );
        final relapsedAtParam = relapsedAtValue == null
            ? null
            : DateTime.fromMillisecondsSinceEpoch(relapsedAtValue);
        final survivorJsonParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 36);
        final healthCenterJsonParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 38);
        final relapseJsonParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 40);
        final creatorJsonParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 42);
        final object = InstanceLocalModel(
          oid: oidParam,
          id: idParam,
          code: codeParam,
          type: typeParam,
          status: statusParam,
          survivorCode: survivorCodeParam,
          healthCenterId: healthCenterIdParam,
          localId: localIdParam,
          description: descriptionParam,
          createdAt: createdAtParam,
          updatedAt: updatedAtParam,
          syncStatus: syncStatusParam,
          lastSyncAttempt: lastSyncAttemptParam,
          syncError: syncErrorParam,
          isDeleted: isDeletedParam,
          relapsedAt: relapsedAtParam,
          survivorJson: survivorJsonParam,
          healthCenterJson: healthCenterJsonParam,
          relapseJson: relapseJsonParam,
          creatorJson: creatorJsonParam,
        );

        return object;
      },
    ),
    FollowupLocalModel: obx_int.EntityDefinition<FollowupLocalModel>(
      model: _entities[2],
      toOneRelations: (FollowupLocalModel object) => [],
      toManyRelations: (FollowupLocalModel object) => {},
      getId: (FollowupLocalModel object) => object.oid,
      setId: (FollowupLocalModel object, int id) {
        object.oid = id;
      },
      objectToFB: (FollowupLocalModel object, fb.Builder fbb) {
        final idOffset = object.id == null ? null : fbb.writeString(object.id!);
        final titleOffset = fbb.writeString(object.title);
        final descriptionOffset = fbb.writeString(object.description);
        final companionIdOffset = fbb.writeString(object.companionId);
        final instanceIdOffset = object.instanceId == null
            ? null
            : fbb.writeString(object.instanceId!);
        final syncStatusOffset = fbb.writeString(object.syncStatus);
        final localIdOffset = fbb.writeString(object.localId);
        final syncErrorOffset = object.syncError == null
            ? null
            : fbb.writeString(object.syncError!);
        fbb.startTable(14);
        fbb.addInt64(0, object.oid);
        fbb.addOffset(1, idOffset);
        fbb.addOffset(2, titleOffset);
        fbb.addOffset(3, descriptionOffset);
        fbb.addOffset(4, companionIdOffset);
        fbb.addOffset(5, instanceIdOffset);
        fbb.addInt64(6, object.createdAt.millisecondsSinceEpoch);
        fbb.addInt64(7, object.updatedAt.millisecondsSinceEpoch);
        fbb.addOffset(8, syncStatusOffset);
        fbb.addOffset(9, localIdOffset);
        fbb.addInt64(10, object.lastSyncAttempt?.millisecondsSinceEpoch);
        fbb.addOffset(11, syncErrorOffset);
        fbb.addBool(12, object.isDeleted);
        fbb.finish(fbb.endTable());
        return object.oid;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final lastSyncAttemptValue = const fb.Int64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          24,
        );
        final oidParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final idParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 6);
        final titleParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final descriptionParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final companionIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 12, '');
        final instanceIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 14);
        final createdAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 16, 0),
        );
        final updatedAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 18, 0),
        );
        final localIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 22, '');
        final syncStatusParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 20, '');
        final lastSyncAttemptParam = lastSyncAttemptValue == null
            ? null
            : DateTime.fromMillisecondsSinceEpoch(lastSyncAttemptValue);
        final syncErrorParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 26);
        final isDeletedParam = const fb.BoolReader().vTableGet(
          buffer,
          rootOffset,
          28,
          false,
        );
        final object = FollowupLocalModel(
          oid: oidParam,
          id: idParam,
          title: titleParam,
          description: descriptionParam,
          companionId: companionIdParam,
          instanceId: instanceIdParam,
          createdAt: createdAtParam,
          updatedAt: updatedAtParam,
          localId: localIdParam,
          syncStatus: syncStatusParam,
          lastSyncAttempt: lastSyncAttemptParam,
          syncError: syncErrorParam,
          isDeleted: isDeletedParam,
        );

        return object;
      },
    ),
    CompanionLocalModel: obx_int.EntityDefinition<CompanionLocalModel>(
      model: _entities[3],
      toOneRelations: (CompanionLocalModel object) => [],
      toManyRelations: (CompanionLocalModel object) => {},
      getId: (CompanionLocalModel object) => object.oid,
      setId: (CompanionLocalModel object, int id) {
        object.oid = id;
      },
      objectToFB: (CompanionLocalModel object, fb.Builder fbb) {
        final idOffset = object.id == null ? null : fbb.writeString(object.id!);
        final typeOffset = fbb.writeString(object.type);
        final instanceIdOffset = fbb.writeString(object.instanceId);
        final localIdOffset = fbb.writeString(object.localId);
        final instanceJsonOffset = fbb.writeString(object.instanceJson);
        fbb.startTable(13);
        fbb.addInt64(0, object.oid);
        fbb.addOffset(1, idOffset);
        fbb.addOffset(2, typeOffset);
        fbb.addOffset(3, instanceIdOffset);
        fbb.addInt64(4, object.createdAt.millisecondsSinceEpoch);
        fbb.addInt64(5, object.updatedAt.millisecondsSinceEpoch);
        fbb.addOffset(7, localIdOffset);
        fbb.addBool(10, object.isDeleted);
        fbb.addOffset(11, instanceJsonOffset);
        fbb.finish(fbb.endTable());
        return object.oid;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final oidParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final idParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 6);
        final typeParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final instanceIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final createdAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 12, 0),
        );
        final updatedAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 14, 0),
        );
        final localIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 18, '');
        final isDeletedParam = const fb.BoolReader().vTableGet(
          buffer,
          rootOffset,
          24,
          false,
        );
        final instanceJsonParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 26, '');
        final object = CompanionLocalModel(
          oid: oidParam,
          id: idParam,
          type: typeParam,
          instanceId: instanceIdParam,
          createdAt: createdAtParam,
          updatedAt: updatedAtParam,
          localId: localIdParam,
          isDeleted: isDeletedParam,
          instanceJson: instanceJsonParam,
        );

        return object;
      },
    ),
    HealthCenterLocalModel: obx_int.EntityDefinition<HealthCenterLocalModel>(
      model: _entities[4],
      toOneRelations: (HealthCenterLocalModel object) => [],
      toManyRelations: (HealthCenterLocalModel object) => {},
      getId: (HealthCenterLocalModel object) => object.oid,
      setId: (HealthCenterLocalModel object, int id) {
        object.oid = id;
      },
      objectToFB: (HealthCenterLocalModel object, fb.Builder fbb) {
        final idOffset = fbb.writeString(object.id);
        final nameOffset = fbb.writeString(object.name);
        final addressOffset = fbb.writeString(object.address);
        final phoneOffset = fbb.writeString(object.phone);
        final servicesOfferedOffset = object.servicesOffered == null
            ? null
            : fbb.writeString(object.servicesOffered!);
        final responsibilityOffset = object.responsibility == null
            ? null
            : fbb.writeString(object.responsibility!);
        final healthZoneJsonOffset = fbb.writeString(object.healthZoneJson);
        fbb.startTable(16);
        fbb.addInt64(0, object.oid);
        fbb.addOffset(1, idOffset);
        fbb.addOffset(2, nameOffset);
        fbb.addOffset(3, addressOffset);
        fbb.addOffset(4, phoneOffset);
        fbb.addOffset(5, servicesOfferedOffset);
        fbb.addInt64(6, object.aps);
        fbb.addInt64(7, object.companions);
        fbb.addInt64(8, object.instances);
        fbb.addInt64(9, object.relapses);
        fbb.addOffset(10, responsibilityOffset);
        fbb.addOffset(11, healthZoneJsonOffset);
        fbb.addInt64(12, object.createdAt.millisecondsSinceEpoch);
        fbb.addInt64(13, object.updatedAt.millisecondsSinceEpoch);
        fbb.addInt64(14, object.lastSyncedAt?.millisecondsSinceEpoch);
        fbb.finish(fbb.endTable());
        return object.oid;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final lastSyncedAtValue = const fb.Int64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          32,
        );
        final oidParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final idParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final nameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final addressParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final phoneParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 12, '');
        final servicesOfferedParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 14);
        final apsParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          16,
          0,
        );
        final companionsParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          18,
          0,
        );
        final instancesParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          20,
          0,
        );
        final relapsesParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          22,
          0,
        );
        final responsibilityParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 24);
        final healthZoneJsonParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 26, '');
        final createdAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 28, 0),
        );
        final updatedAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 30, 0),
        );
        final lastSyncedAtParam = lastSyncedAtValue == null
            ? null
            : DateTime.fromMillisecondsSinceEpoch(lastSyncedAtValue);
        final object = HealthCenterLocalModel(
          oid: oidParam,
          id: idParam,
          name: nameParam,
          address: addressParam,
          phone: phoneParam,
          servicesOffered: servicesOfferedParam,
          aps: apsParam,
          companions: companionsParam,
          instances: instancesParam,
          relapses: relapsesParam,
          responsibility: responsibilityParam,
          healthZoneJson: healthZoneJsonParam,
          createdAt: createdAtParam,
          updatedAt: updatedAtParam,
          lastSyncedAt: lastSyncedAtParam,
        );

        return object;
      },
    ),
    HealthZoneLocalModel: obx_int.EntityDefinition<HealthZoneLocalModel>(
      model: _entities[5],
      toOneRelations: (HealthZoneLocalModel object) => [],
      toManyRelations: (HealthZoneLocalModel object) => {},
      getId: (HealthZoneLocalModel object) => object.oid,
      setId: (HealthZoneLocalModel object, int id) {
        object.oid = id;
      },
      objectToFB: (HealthZoneLocalModel object, fb.Builder fbb) {
        final idOffset = fbb.writeString(object.id);
        final nameOffset = fbb.writeString(object.name);
        fbb.startTable(7);
        fbb.addInt64(0, object.oid);
        fbb.addOffset(1, idOffset);
        fbb.addOffset(2, nameOffset);
        fbb.addInt64(3, object.populationServed);
        fbb.addInt64(4, object.createdAt.millisecondsSinceEpoch);
        fbb.addInt64(5, object.updatedAt.millisecondsSinceEpoch);
        fbb.finish(fbb.endTable());
        return object.oid;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final oidParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          4,
          0,
        );
        final idParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final nameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final populationServedParam = const fb.Int64Reader().vTableGet(
          buffer,
          rootOffset,
          10,
          0,
        );
        final createdAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 12, 0),
        );
        final updatedAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 14, 0),
        );
        final object = HealthZoneLocalModel(
          oid: oidParam,
          id: idParam,
          name: nameParam,
          populationServed: populationServedParam,
          createdAt: createdAtParam,
          updatedAt: updatedAtParam,
        );

        return object;
      },
    ),
    CompanionRelapseLocalModel:
        obx_int.EntityDefinition<CompanionRelapseLocalModel>(
          model: _entities[6],
          toOneRelations: (CompanionRelapseLocalModel object) => [],
          toManyRelations: (CompanionRelapseLocalModel object) => {},
          getId: (CompanionRelapseLocalModel object) => object.oid,
          setId: (CompanionRelapseLocalModel object, int id) {
            object.oid = id;
          },
          objectToFB: (CompanionRelapseLocalModel object, fb.Builder fbb) {
            final idOffset = object.id == null
                ? null
                : fbb.writeString(object.id!);
            final localIdOffset = fbb.writeString(object.localId);
            final companionIdOffset = fbb.writeString(object.companionId);
            final descriptionOffset = fbb.writeString(object.description);
            final syncStatusOffset = fbb.writeString(object.syncStatus);
            final syncErrorOffset = object.syncError == null
                ? null
                : fbb.writeString(object.syncError!);
            fbb.startTable(12);
            fbb.addInt64(0, object.oid);
            fbb.addOffset(1, idOffset);
            fbb.addOffset(2, localIdOffset);
            fbb.addOffset(3, companionIdOffset);
            fbb.addOffset(4, descriptionOffset);
            fbb.addOffset(5, syncStatusOffset);
            fbb.addInt64(6, object.lastSyncAttempt?.millisecondsSinceEpoch);
            fbb.addOffset(7, syncErrorOffset);
            fbb.addBool(8, object.isDeleted);
            fbb.addInt64(9, object.createdAt.millisecondsSinceEpoch);
            fbb.addInt64(10, object.updatedAt.millisecondsSinceEpoch);
            fbb.finish(fbb.endTable());
            return object.oid;
          },
          objectFromFB: (obx.Store store, ByteData fbData) {
            final buffer = fb.BufferContext(fbData);
            final rootOffset = buffer.derefObject(0);
            final lastSyncAttemptValue = const fb.Int64Reader()
                .vTableGetNullable(buffer, rootOffset, 16);
            final idParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGetNullable(buffer, rootOffset, 6);
            final localIdParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 8, '');
            final companionIdParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 10, '');
            final descriptionParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 12, '');
            final syncStatusParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 14, '');
            final lastSyncAttemptParam = lastSyncAttemptValue == null
                ? null
                : DateTime.fromMillisecondsSinceEpoch(lastSyncAttemptValue);
            final syncErrorParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGetNullable(buffer, rootOffset, 18);
            final isDeletedParam = const fb.BoolReader().vTableGet(
              buffer,
              rootOffset,
              20,
              false,
            );
            final createdAtParam = DateTime.fromMillisecondsSinceEpoch(
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 22, 0),
            );
            final updatedAtParam = DateTime.fromMillisecondsSinceEpoch(
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 24, 0),
            );
            final object = CompanionRelapseLocalModel(
              id: idParam,
              localId: localIdParam,
              companionId: companionIdParam,
              description: descriptionParam,
              syncStatus: syncStatusParam,
              lastSyncAttempt: lastSyncAttemptParam,
              syncError: syncErrorParam,
              isDeleted: isDeletedParam,
              createdAt: createdAtParam,
              updatedAt: updatedAtParam,
            )..oid = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

            return object;
          },
        ),
    InstanceRelapseLocalModel:
        obx_int.EntityDefinition<InstanceRelapseLocalModel>(
          model: _entities[7],
          toOneRelations: (InstanceRelapseLocalModel object) => [],
          toManyRelations: (InstanceRelapseLocalModel object) => {},
          getId: (InstanceRelapseLocalModel object) => object.oid,
          setId: (InstanceRelapseLocalModel object, int id) {
            object.oid = id;
          },
          objectToFB: (InstanceRelapseLocalModel object, fb.Builder fbb) {
            final idOffset = object.id == null
                ? null
                : fbb.writeString(object.id!);
            final localIdOffset = fbb.writeString(object.localId);
            final descriptionOffset = fbb.writeString(object.description);
            final syncStatusOffset = fbb.writeString(object.syncStatus);
            final syncErrorOffset = object.syncError == null
                ? null
                : fbb.writeString(object.syncError!);
            final instanceLocalIdOffset = fbb.writeString(
              object.instanceLocalId,
            );
            fbb.startTable(13);
            fbb.addInt64(0, object.oid);
            fbb.addOffset(1, idOffset);
            fbb.addOffset(2, localIdOffset);
            fbb.addOffset(4, descriptionOffset);
            fbb.addOffset(5, syncStatusOffset);
            fbb.addInt64(6, object.lastSyncAttempt?.millisecondsSinceEpoch);
            fbb.addOffset(7, syncErrorOffset);
            fbb.addBool(8, object.isDeleted);
            fbb.addInt64(9, object.createdAt.millisecondsSinceEpoch);
            fbb.addInt64(10, object.updatedAt.millisecondsSinceEpoch);
            fbb.addOffset(11, instanceLocalIdOffset);
            fbb.finish(fbb.endTable());
            return object.oid;
          },
          objectFromFB: (obx.Store store, ByteData fbData) {
            final buffer = fb.BufferContext(fbData);
            final rootOffset = buffer.derefObject(0);
            final lastSyncAttemptValue = const fb.Int64Reader()
                .vTableGetNullable(buffer, rootOffset, 16);
            final idParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGetNullable(buffer, rootOffset, 6);
            final localIdParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 8, '');
            final instanceLocalIdParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 26, '');
            final descriptionParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 12, '');
            final syncStatusParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 14, '');
            final lastSyncAttemptParam = lastSyncAttemptValue == null
                ? null
                : DateTime.fromMillisecondsSinceEpoch(lastSyncAttemptValue);
            final syncErrorParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGetNullable(buffer, rootOffset, 18);
            final isDeletedParam = const fb.BoolReader().vTableGet(
              buffer,
              rootOffset,
              20,
              false,
            );
            final createdAtParam = DateTime.fromMillisecondsSinceEpoch(
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 22, 0),
            );
            final updatedAtParam = DateTime.fromMillisecondsSinceEpoch(
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 24, 0),
            );
            final object = InstanceRelapseLocalModel(
              id: idParam,
              localId: localIdParam,
              instanceLocalId: instanceLocalIdParam,
              description: descriptionParam,
              syncStatus: syncStatusParam,
              lastSyncAttempt: lastSyncAttemptParam,
              syncError: syncErrorParam,
              isDeleted: isDeletedParam,
              createdAt: createdAtParam,
              updatedAt: updatedAtParam,
            )..oid = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

            return object;
          },
        ),
    InstanceCompanionLocalModel:
        obx_int.EntityDefinition<InstanceCompanionLocalModel>(
          model: _entities[8],
          toOneRelations: (InstanceCompanionLocalModel object) => [],
          toManyRelations: (InstanceCompanionLocalModel object) => {},
          getId: (InstanceCompanionLocalModel object) => object.oid,
          setId: (InstanceCompanionLocalModel object, int id) {
            object.oid = id;
          },
          objectToFB: (InstanceCompanionLocalModel object, fb.Builder fbb) {
            final idOffset = object.id == null
                ? null
                : fbb.writeString(object.id!);
            final localIdOffset = fbb.writeString(object.localId);
            final instanceLocalIdOffset = fbb.writeString(
              object.instanceLocalId,
            );
            final typeOffset = fbb.writeString(object.type);
            final userJsonOffset = fbb.writeString(object.userJson);
            final syncStatusOffset = fbb.writeString(object.syncStatus);
            final syncErrorOffset = object.syncError == null
                ? null
                : fbb.writeString(object.syncError!);
            fbb.startTable(13);
            fbb.addInt64(0, object.oid);
            fbb.addOffset(1, idOffset);
            fbb.addOffset(2, localIdOffset);
            fbb.addOffset(3, instanceLocalIdOffset);
            fbb.addOffset(4, typeOffset);
            fbb.addOffset(5, userJsonOffset);
            fbb.addOffset(6, syncStatusOffset);
            fbb.addInt64(7, object.lastSyncAttempt?.millisecondsSinceEpoch);
            fbb.addOffset(8, syncErrorOffset);
            fbb.addBool(9, object.isDeleted);
            fbb.addInt64(10, object.createdAt.millisecondsSinceEpoch);
            fbb.addInt64(11, object.updatedAt.millisecondsSinceEpoch);
            fbb.finish(fbb.endTable());
            return object.oid;
          },
          objectFromFB: (obx.Store store, ByteData fbData) {
            final buffer = fb.BufferContext(fbData);
            final rootOffset = buffer.derefObject(0);
            final lastSyncAttemptValue = const fb.Int64Reader()
                .vTableGetNullable(buffer, rootOffset, 18);
            final idParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGetNullable(buffer, rootOffset, 6);
            final localIdParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 8, '');
            final instanceLocalIdParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 10, '');
            final typeParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 12, '');
            final userJsonParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 14, '');
            final syncStatusParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGet(buffer, rootOffset, 16, '');
            final lastSyncAttemptParam = lastSyncAttemptValue == null
                ? null
                : DateTime.fromMillisecondsSinceEpoch(lastSyncAttemptValue);
            final syncErrorParam = const fb.StringReader(
              asciiOptimization: true,
            ).vTableGetNullable(buffer, rootOffset, 20);
            final isDeletedParam = const fb.BoolReader().vTableGet(
              buffer,
              rootOffset,
              22,
              false,
            );
            final createdAtParam = DateTime.fromMillisecondsSinceEpoch(
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 24, 0),
            );
            final updatedAtParam = DateTime.fromMillisecondsSinceEpoch(
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 26, 0),
            );
            final object = InstanceCompanionLocalModel(
              id: idParam,
              localId: localIdParam,
              instanceLocalId: instanceLocalIdParam,
              type: typeParam,
              userJson: userJsonParam,
              syncStatus: syncStatusParam,
              lastSyncAttempt: lastSyncAttemptParam,
              syncError: syncErrorParam,
              isDeleted: isDeletedParam,
              createdAt: createdAtParam,
              updatedAt: updatedAtParam,
            )..oid = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

            return object;
          },
        ),
    MemberLocalModel: obx_int.EntityDefinition<MemberLocalModel>(
      model: _entities[9],
      toOneRelations: (MemberLocalModel object) => [],
      toManyRelations: (MemberLocalModel object) => {},
      getId: (MemberLocalModel object) => object.oid,
      setId: (MemberLocalModel object, int id) {
        object.oid = id;
      },
      objectToFB: (MemberLocalModel object, fb.Builder fbb) {
        final idOffset = fbb.writeString(object.id);
        final nameOffset = fbb.writeString(object.name);
        final emailOffset = object.email == null
            ? null
            : fbb.writeString(object.email!);
        final roleOffset = fbb.writeString(object.role);
        final responsibilityOffset = fbb.writeString(object.responsibility);
        final companionRoleOffset = object.companionRole == null
            ? null
            : fbb.writeString(object.companionRole!);
        final phoneOffset = fbb.writeString(object.phone);
        final descriptionOffset = object.description == null
            ? null
            : fbb.writeString(object.description!);
        final healthCenterIdOffset = fbb.writeString(object.healthCenterId);
        fbb.startTable(13);
        fbb.addInt64(0, object.oid);
        fbb.addOffset(1, idOffset);
        fbb.addOffset(2, nameOffset);
        fbb.addOffset(3, emailOffset);
        fbb.addOffset(4, roleOffset);
        fbb.addOffset(5, responsibilityOffset);
        fbb.addOffset(6, companionRoleOffset);
        fbb.addOffset(7, phoneOffset);
        fbb.addOffset(8, descriptionOffset);
        fbb.addInt64(9, object.createdAt.millisecondsSinceEpoch);
        fbb.addInt64(10, object.updatedAt.millisecondsSinceEpoch);
        fbb.addOffset(11, healthCenterIdOffset);
        fbb.finish(fbb.endTable());
        return object.oid;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final idParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final nameParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final emailParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 10);
        final roleParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 12, '');
        final healthCenterIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 26, '');
        final responsibilityParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 14, '');
        final companionRoleParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 16);
        final phoneParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 18, '');
        final descriptionParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 20);
        final createdAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 22, 0),
        );
        final updatedAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 24, 0),
        );
        final object = MemberLocalModel(
          id: idParam,
          name: nameParam,
          email: emailParam,
          role: roleParam,
          healthCenterId: healthCenterIdParam,
          responsibility: responsibilityParam,
          companionRole: companionRoleParam,
          phone: phoneParam,
          description: descriptionParam,
          createdAt: createdAtParam,
          updatedAt: updatedAtParam,
        )..oid = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

        return object;
      },
    ),
    QuestionnaireLocalModel: obx_int.EntityDefinition<QuestionnaireLocalModel>(
      model: _entities[10],
      toOneRelations: (QuestionnaireLocalModel object) => [],
      toManyRelations: (QuestionnaireLocalModel object) => {},
      getId: (QuestionnaireLocalModel object) => object.oid,
      setId: (QuestionnaireLocalModel object, int id) {
        object.oid = id;
      },
      objectToFB: (QuestionnaireLocalModel object, fb.Builder fbb) {
        final idOffset = fbb.writeString(object.id);
        final questionOffset = fbb.writeString(object.question);
        final typeOffset = fbb.writeString(object.type);
        final hintOffset = object.hint == null
            ? null
            : fbb.writeString(object.hint!);
        final choicesJsonOffset = fbb.writeString(object.choicesJson);
        fbb.startTable(9);
        fbb.addInt64(0, object.oid);
        fbb.addOffset(1, idOffset);
        fbb.addOffset(2, questionOffset);
        fbb.addOffset(3, typeOffset);
        fbb.addOffset(4, hintOffset);
        fbb.addInt64(5, object.createdAt.millisecondsSinceEpoch);
        fbb.addInt64(6, object.updatedAt.millisecondsSinceEpoch);
        fbb.addOffset(7, choicesJsonOffset);
        fbb.finish(fbb.endTable());
        return object.oid;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final idParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 6, '');
        final questionParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final typeParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final choicesJsonParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 18, '');
        final hintParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 12);
        final createdAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 14, 0),
        );
        final updatedAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 16, 0),
        );
        final object = QuestionnaireLocalModel(
          id: idParam,
          question: questionParam,
          type: typeParam,
          choicesJson: choicesJsonParam,
          hint: hintParam,
          createdAt: createdAtParam,
          updatedAt: updatedAtParam,
        )..oid = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

        return object;
      },
    ),
    TreatmentLocalModel: obx_int.EntityDefinition<TreatmentLocalModel>(
      model: _entities[11],
      toOneRelations: (TreatmentLocalModel object) => [],
      toManyRelations: (TreatmentLocalModel object) => {},
      getId: (TreatmentLocalModel object) => object.oid,
      setId: (TreatmentLocalModel object, int id) {
        object.oid = id;
      },
      objectToFB: (TreatmentLocalModel object, fb.Builder fbb) {
        final idOffset = object.id == null ? null : fbb.writeString(object.id!);
        final localIdOffset = fbb.writeString(object.localId);
        final instanceLocalIdOffset = fbb.writeString(object.instanceLocalId);
        final typeOffset = fbb.writeString(object.type);
        final observationOffset = object.observation == null
            ? null
            : fbb.writeString(object.observation!);
        final attachmentOffset = object.attachment == null
            ? null
            : fbb.writeString(object.attachment!);
        final syncStatusOffset = fbb.writeString(object.syncStatus);
        final syncErrorOffset = object.syncError == null
            ? null
            : fbb.writeString(object.syncError!);
        fbb.startTable(14);
        fbb.addInt64(0, object.oid);
        fbb.addOffset(1, idOffset);
        fbb.addOffset(2, localIdOffset);
        fbb.addOffset(3, instanceLocalIdOffset);
        fbb.addOffset(4, typeOffset);
        fbb.addOffset(5, observationOffset);
        fbb.addOffset(6, attachmentOffset);
        fbb.addOffset(7, syncStatusOffset);
        fbb.addInt64(8, object.lastSyncAttempt?.millisecondsSinceEpoch);
        fbb.addOffset(9, syncErrorOffset);
        fbb.addBool(10, object.isDeleted);
        fbb.addInt64(11, object.createdAt.millisecondsSinceEpoch);
        fbb.addInt64(12, object.updatedAt.millisecondsSinceEpoch);
        fbb.finish(fbb.endTable());
        return object.oid;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final lastSyncAttemptValue = const fb.Int64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          20,
        );
        final idParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 6);
        final localIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final instanceLocalIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final typeParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 12, '');
        final observationParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 14);
        final attachmentParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 16);
        final syncStatusParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 18, '');
        final lastSyncAttemptParam = lastSyncAttemptValue == null
            ? null
            : DateTime.fromMillisecondsSinceEpoch(lastSyncAttemptValue);
        final syncErrorParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 22);
        final isDeletedParam = const fb.BoolReader().vTableGet(
          buffer,
          rootOffset,
          24,
          false,
        );
        final createdAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 26, 0),
        );
        final updatedAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 28, 0),
        );
        final object = TreatmentLocalModel(
          id: idParam,
          localId: localIdParam,
          instanceLocalId: instanceLocalIdParam,
          type: typeParam,
          observation: observationParam,
          attachment: attachmentParam,
          syncStatus: syncStatusParam,
          lastSyncAttempt: lastSyncAttemptParam,
          syncError: syncErrorParam,
          isDeleted: isDeletedParam,
          createdAt: createdAtParam,
          updatedAt: updatedAtParam,
        )..oid = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

        return object;
      },
    ),
    DiagnosticLocalModel: obx_int.EntityDefinition<DiagnosticLocalModel>(
      model: _entities[12],
      toOneRelations: (DiagnosticLocalModel object) => [],
      toManyRelations: (DiagnosticLocalModel object) => {},
      getId: (DiagnosticLocalModel object) => object.oid,
      setId: (DiagnosticLocalModel object, int id) {
        object.oid = id;
      },
      objectToFB: (DiagnosticLocalModel object, fb.Builder fbb) {
        final idOffset = object.id == null ? null : fbb.writeString(object.id!);
        final localIdOffset = fbb.writeString(object.localId);
        final instanceLocalIdOffset = fbb.writeString(object.instanceLocalId);
        final questionnaireIdOffset = fbb.writeString(object.questionnaireId);
        final responseOffset = fbb.writeString(object.response);
        final syncStatusOffset = fbb.writeString(object.syncStatus);
        final syncErrorOffset = object.syncError == null
            ? null
            : fbb.writeString(object.syncError!);
        fbb.startTable(13);
        fbb.addInt64(0, object.oid);
        fbb.addOffset(1, idOffset);
        fbb.addOffset(2, localIdOffset);
        fbb.addOffset(3, instanceLocalIdOffset);
        fbb.addOffset(4, questionnaireIdOffset);
        fbb.addOffset(5, responseOffset);
        fbb.addOffset(6, syncStatusOffset);
        fbb.addInt64(7, object.lastSyncAttempt?.millisecondsSinceEpoch);
        fbb.addOffset(8, syncErrorOffset);
        fbb.addBool(9, object.isDeleted);
        fbb.addInt64(10, object.createdAt.millisecondsSinceEpoch);
        fbb.addInt64(11, object.updatedAt.millisecondsSinceEpoch);
        fbb.finish(fbb.endTable());
        return object.oid;
      },
      objectFromFB: (obx.Store store, ByteData fbData) {
        final buffer = fb.BufferContext(fbData);
        final rootOffset = buffer.derefObject(0);
        final lastSyncAttemptValue = const fb.Int64Reader().vTableGetNullable(
          buffer,
          rootOffset,
          18,
        );
        final idParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 6);
        final localIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 8, '');
        final instanceLocalIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 10, '');
        final questionnaireIdParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 12, '');
        final responseParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 14, '');
        final syncStatusParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGet(buffer, rootOffset, 16, '');
        final lastSyncAttemptParam = lastSyncAttemptValue == null
            ? null
            : DateTime.fromMillisecondsSinceEpoch(lastSyncAttemptValue);
        final syncErrorParam = const fb.StringReader(
          asciiOptimization: true,
        ).vTableGetNullable(buffer, rootOffset, 20);
        final isDeletedParam = const fb.BoolReader().vTableGet(
          buffer,
          rootOffset,
          22,
          false,
        );
        final createdAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 24, 0),
        );
        final updatedAtParam = DateTime.fromMillisecondsSinceEpoch(
          const fb.Int64Reader().vTableGet(buffer, rootOffset, 26, 0),
        );
        final object = DiagnosticLocalModel(
          id: idParam,
          localId: localIdParam,
          instanceLocalId: instanceLocalIdParam,
          questionnaireId: questionnaireIdParam,
          response: responseParam,
          syncStatus: syncStatusParam,
          lastSyncAttempt: lastSyncAttemptParam,
          syncError: syncErrorParam,
          isDeleted: isDeletedParam,
          createdAt: createdAtParam,
          updatedAt: updatedAtParam,
        )..oid = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

        return object;
      },
    ),
  };

  return obx_int.ModelDefinition(model, bindings);
}

/// [UserLocalModel] entity fields to define ObjectBox queries.
class UserLocalModel_ {
  /// See [UserLocalModel.id].
  static final id = obx.QueryStringProperty<UserLocalModel>(
    _entities[0].properties[0],
  );

  /// See [UserLocalModel.name].
  static final name = obx.QueryStringProperty<UserLocalModel>(
    _entities[0].properties[1],
  );

  /// See [UserLocalModel.role].
  static final role = obx.QueryStringProperty<UserLocalModel>(
    _entities[0].properties[2],
  );

  /// See [UserLocalModel.phone].
  static final phone = obx.QueryStringProperty<UserLocalModel>(
    _entities[0].properties[3],
  );

  /// See [UserLocalModel.email].
  static final email = obx.QueryStringProperty<UserLocalModel>(
    _entities[0].properties[4],
  );

  /// See [UserLocalModel.description].
  static final description = obx.QueryStringProperty<UserLocalModel>(
    _entities[0].properties[5],
  );

  /// See [UserLocalModel.createdAt].
  static final createdAt = obx.QueryDateProperty<UserLocalModel>(
    _entities[0].properties[6],
  );

  /// See [UserLocalModel.updatedAt].
  static final updatedAt = obx.QueryDateProperty<UserLocalModel>(
    _entities[0].properties[7],
  );

  /// See [UserLocalModel.oid].
  static final oid = obx.QueryIntegerProperty<UserLocalModel>(
    _entities[0].properties[8],
  );
}

/// [InstanceLocalModel] entity fields to define ObjectBox queries.
class InstanceLocalModel_ {
  /// See [InstanceLocalModel.oid].
  static final oid = obx.QueryIntegerProperty<InstanceLocalModel>(
    _entities[1].properties[0],
  );

  /// See [InstanceLocalModel.id].
  static final id = obx.QueryStringProperty<InstanceLocalModel>(
    _entities[1].properties[1],
  );

  /// See [InstanceLocalModel.code].
  static final code = obx.QueryStringProperty<InstanceLocalModel>(
    _entities[1].properties[2],
  );

  /// See [InstanceLocalModel.type].
  static final type = obx.QueryStringProperty<InstanceLocalModel>(
    _entities[1].properties[3],
  );

  /// See [InstanceLocalModel.status].
  static final status = obx.QueryStringProperty<InstanceLocalModel>(
    _entities[1].properties[4],
  );

  /// See [InstanceLocalModel.survivorCode].
  static final survivorCode = obx.QueryStringProperty<InstanceLocalModel>(
    _entities[1].properties[5],
  );

  /// See [InstanceLocalModel.healthCenterId].
  static final healthCenterId = obx.QueryStringProperty<InstanceLocalModel>(
    _entities[1].properties[6],
  );

  /// See [InstanceLocalModel.description].
  static final description = obx.QueryStringProperty<InstanceLocalModel>(
    _entities[1].properties[7],
  );

  /// See [InstanceLocalModel.createdAt].
  static final createdAt = obx.QueryDateProperty<InstanceLocalModel>(
    _entities[1].properties[8],
  );

  /// See [InstanceLocalModel.updatedAt].
  static final updatedAt = obx.QueryDateProperty<InstanceLocalModel>(
    _entities[1].properties[9],
  );

  /// See [InstanceLocalModel.syncStatus].
  static final syncStatus = obx.QueryStringProperty<InstanceLocalModel>(
    _entities[1].properties[10],
  );

  /// See [InstanceLocalModel.localId].
  static final localId = obx.QueryStringProperty<InstanceLocalModel>(
    _entities[1].properties[11],
  );

  /// See [InstanceLocalModel.lastSyncAttempt].
  static final lastSyncAttempt = obx.QueryDateProperty<InstanceLocalModel>(
    _entities[1].properties[12],
  );

  /// See [InstanceLocalModel.syncError].
  static final syncError = obx.QueryStringProperty<InstanceLocalModel>(
    _entities[1].properties[13],
  );

  /// See [InstanceLocalModel.isDeleted].
  static final isDeleted = obx.QueryBooleanProperty<InstanceLocalModel>(
    _entities[1].properties[14],
  );

  /// See [InstanceLocalModel.relapsedAt].
  static final relapsedAt = obx.QueryDateProperty<InstanceLocalModel>(
    _entities[1].properties[15],
  );

  /// See [InstanceLocalModel.survivorJson].
  static final survivorJson = obx.QueryStringProperty<InstanceLocalModel>(
    _entities[1].properties[16],
  );

  /// See [InstanceLocalModel.healthCenterJson].
  static final healthCenterJson = obx.QueryStringProperty<InstanceLocalModel>(
    _entities[1].properties[17],
  );

  /// See [InstanceLocalModel.relapseJson].
  static final relapseJson = obx.QueryStringProperty<InstanceLocalModel>(
    _entities[1].properties[18],
  );

  /// See [InstanceLocalModel.creatorJson].
  static final creatorJson = obx.QueryStringProperty<InstanceLocalModel>(
    _entities[1].properties[19],
  );
}

/// [FollowupLocalModel] entity fields to define ObjectBox queries.
class FollowupLocalModel_ {
  /// See [FollowupLocalModel.oid].
  static final oid = obx.QueryIntegerProperty<FollowupLocalModel>(
    _entities[2].properties[0],
  );

  /// See [FollowupLocalModel.id].
  static final id = obx.QueryStringProperty<FollowupLocalModel>(
    _entities[2].properties[1],
  );

  /// See [FollowupLocalModel.title].
  static final title = obx.QueryStringProperty<FollowupLocalModel>(
    _entities[2].properties[2],
  );

  /// See [FollowupLocalModel.description].
  static final description = obx.QueryStringProperty<FollowupLocalModel>(
    _entities[2].properties[3],
  );

  /// See [FollowupLocalModel.companionId].
  static final companionId = obx.QueryStringProperty<FollowupLocalModel>(
    _entities[2].properties[4],
  );

  /// See [FollowupLocalModel.instanceId].
  static final instanceId = obx.QueryStringProperty<FollowupLocalModel>(
    _entities[2].properties[5],
  );

  /// See [FollowupLocalModel.createdAt].
  static final createdAt = obx.QueryDateProperty<FollowupLocalModel>(
    _entities[2].properties[6],
  );

  /// See [FollowupLocalModel.updatedAt].
  static final updatedAt = obx.QueryDateProperty<FollowupLocalModel>(
    _entities[2].properties[7],
  );

  /// See [FollowupLocalModel.syncStatus].
  static final syncStatus = obx.QueryStringProperty<FollowupLocalModel>(
    _entities[2].properties[8],
  );

  /// See [FollowupLocalModel.localId].
  static final localId = obx.QueryStringProperty<FollowupLocalModel>(
    _entities[2].properties[9],
  );

  /// See [FollowupLocalModel.lastSyncAttempt].
  static final lastSyncAttempt = obx.QueryDateProperty<FollowupLocalModel>(
    _entities[2].properties[10],
  );

  /// See [FollowupLocalModel.syncError].
  static final syncError = obx.QueryStringProperty<FollowupLocalModel>(
    _entities[2].properties[11],
  );

  /// See [FollowupLocalModel.isDeleted].
  static final isDeleted = obx.QueryBooleanProperty<FollowupLocalModel>(
    _entities[2].properties[12],
  );
}

/// [CompanionLocalModel] entity fields to define ObjectBox queries.
class CompanionLocalModel_ {
  /// See [CompanionLocalModel.oid].
  static final oid = obx.QueryIntegerProperty<CompanionLocalModel>(
    _entities[3].properties[0],
  );

  /// See [CompanionLocalModel.id].
  static final id = obx.QueryStringProperty<CompanionLocalModel>(
    _entities[3].properties[1],
  );

  /// See [CompanionLocalModel.type].
  static final type = obx.QueryStringProperty<CompanionLocalModel>(
    _entities[3].properties[2],
  );

  /// See [CompanionLocalModel.instanceId].
  static final instanceId = obx.QueryStringProperty<CompanionLocalModel>(
    _entities[3].properties[3],
  );

  /// See [CompanionLocalModel.createdAt].
  static final createdAt = obx.QueryDateProperty<CompanionLocalModel>(
    _entities[3].properties[4],
  );

  /// See [CompanionLocalModel.updatedAt].
  static final updatedAt = obx.QueryDateProperty<CompanionLocalModel>(
    _entities[3].properties[5],
  );

  /// See [CompanionLocalModel.localId].
  static final localId = obx.QueryStringProperty<CompanionLocalModel>(
    _entities[3].properties[6],
  );

  /// See [CompanionLocalModel.isDeleted].
  static final isDeleted = obx.QueryBooleanProperty<CompanionLocalModel>(
    _entities[3].properties[7],
  );

  /// See [CompanionLocalModel.instanceJson].
  static final instanceJson = obx.QueryStringProperty<CompanionLocalModel>(
    _entities[3].properties[8],
  );
}

/// [HealthCenterLocalModel] entity fields to define ObjectBox queries.
class HealthCenterLocalModel_ {
  /// See [HealthCenterLocalModel.oid].
  static final oid = obx.QueryIntegerProperty<HealthCenterLocalModel>(
    _entities[4].properties[0],
  );

  /// See [HealthCenterLocalModel.id].
  static final id = obx.QueryStringProperty<HealthCenterLocalModel>(
    _entities[4].properties[1],
  );

  /// See [HealthCenterLocalModel.name].
  static final name = obx.QueryStringProperty<HealthCenterLocalModel>(
    _entities[4].properties[2],
  );

  /// See [HealthCenterLocalModel.address].
  static final address = obx.QueryStringProperty<HealthCenterLocalModel>(
    _entities[4].properties[3],
  );

  /// See [HealthCenterLocalModel.phone].
  static final phone = obx.QueryStringProperty<HealthCenterLocalModel>(
    _entities[4].properties[4],
  );

  /// See [HealthCenterLocalModel.servicesOffered].
  static final servicesOffered =
      obx.QueryStringProperty<HealthCenterLocalModel>(
        _entities[4].properties[5],
      );

  /// See [HealthCenterLocalModel.aps].
  static final aps = obx.QueryIntegerProperty<HealthCenterLocalModel>(
    _entities[4].properties[6],
  );

  /// See [HealthCenterLocalModel.companions].
  static final companions = obx.QueryIntegerProperty<HealthCenterLocalModel>(
    _entities[4].properties[7],
  );

  /// See [HealthCenterLocalModel.instances].
  static final instances = obx.QueryIntegerProperty<HealthCenterLocalModel>(
    _entities[4].properties[8],
  );

  /// See [HealthCenterLocalModel.relapses].
  static final relapses = obx.QueryIntegerProperty<HealthCenterLocalModel>(
    _entities[4].properties[9],
  );

  /// See [HealthCenterLocalModel.responsibility].
  static final responsibility = obx.QueryStringProperty<HealthCenterLocalModel>(
    _entities[4].properties[10],
  );

  /// See [HealthCenterLocalModel.healthZoneJson].
  static final healthZoneJson = obx.QueryStringProperty<HealthCenterLocalModel>(
    _entities[4].properties[11],
  );

  /// See [HealthCenterLocalModel.createdAt].
  static final createdAt = obx.QueryDateProperty<HealthCenterLocalModel>(
    _entities[4].properties[12],
  );

  /// See [HealthCenterLocalModel.updatedAt].
  static final updatedAt = obx.QueryDateProperty<HealthCenterLocalModel>(
    _entities[4].properties[13],
  );

  /// See [HealthCenterLocalModel.lastSyncedAt].
  static final lastSyncedAt = obx.QueryDateProperty<HealthCenterLocalModel>(
    _entities[4].properties[14],
  );
}

/// [HealthZoneLocalModel] entity fields to define ObjectBox queries.
class HealthZoneLocalModel_ {
  /// See [HealthZoneLocalModel.oid].
  static final oid = obx.QueryIntegerProperty<HealthZoneLocalModel>(
    _entities[5].properties[0],
  );

  /// See [HealthZoneLocalModel.id].
  static final id = obx.QueryStringProperty<HealthZoneLocalModel>(
    _entities[5].properties[1],
  );

  /// See [HealthZoneLocalModel.name].
  static final name = obx.QueryStringProperty<HealthZoneLocalModel>(
    _entities[5].properties[2],
  );

  /// See [HealthZoneLocalModel.populationServed].
  static final populationServed =
      obx.QueryIntegerProperty<HealthZoneLocalModel>(
        _entities[5].properties[3],
      );

  /// See [HealthZoneLocalModel.createdAt].
  static final createdAt = obx.QueryDateProperty<HealthZoneLocalModel>(
    _entities[5].properties[4],
  );

  /// See [HealthZoneLocalModel.updatedAt].
  static final updatedAt = obx.QueryDateProperty<HealthZoneLocalModel>(
    _entities[5].properties[5],
  );
}

/// [CompanionRelapseLocalModel] entity fields to define ObjectBox queries.
class CompanionRelapseLocalModel_ {
  /// See [CompanionRelapseLocalModel.oid].
  static final oid = obx.QueryIntegerProperty<CompanionRelapseLocalModel>(
    _entities[6].properties[0],
  );

  /// See [CompanionRelapseLocalModel.id].
  static final id = obx.QueryStringProperty<CompanionRelapseLocalModel>(
    _entities[6].properties[1],
  );

  /// See [CompanionRelapseLocalModel.localId].
  static final localId = obx.QueryStringProperty<CompanionRelapseLocalModel>(
    _entities[6].properties[2],
  );

  /// See [CompanionRelapseLocalModel.companionId].
  static final companionId =
      obx.QueryStringProperty<CompanionRelapseLocalModel>(
        _entities[6].properties[3],
      );

  /// See [CompanionRelapseLocalModel.description].
  static final description =
      obx.QueryStringProperty<CompanionRelapseLocalModel>(
        _entities[6].properties[4],
      );

  /// See [CompanionRelapseLocalModel.syncStatus].
  static final syncStatus = obx.QueryStringProperty<CompanionRelapseLocalModel>(
    _entities[6].properties[5],
  );

  /// See [CompanionRelapseLocalModel.lastSyncAttempt].
  static final lastSyncAttempt =
      obx.QueryDateProperty<CompanionRelapseLocalModel>(
        _entities[6].properties[6],
      );

  /// See [CompanionRelapseLocalModel.syncError].
  static final syncError = obx.QueryStringProperty<CompanionRelapseLocalModel>(
    _entities[6].properties[7],
  );

  /// See [CompanionRelapseLocalModel.isDeleted].
  static final isDeleted = obx.QueryBooleanProperty<CompanionRelapseLocalModel>(
    _entities[6].properties[8],
  );

  /// See [CompanionRelapseLocalModel.createdAt].
  static final createdAt = obx.QueryDateProperty<CompanionRelapseLocalModel>(
    _entities[6].properties[9],
  );

  /// See [CompanionRelapseLocalModel.updatedAt].
  static final updatedAt = obx.QueryDateProperty<CompanionRelapseLocalModel>(
    _entities[6].properties[10],
  );
}

/// [InstanceRelapseLocalModel] entity fields to define ObjectBox queries.
class InstanceRelapseLocalModel_ {
  /// See [InstanceRelapseLocalModel.oid].
  static final oid = obx.QueryIntegerProperty<InstanceRelapseLocalModel>(
    _entities[7].properties[0],
  );

  /// See [InstanceRelapseLocalModel.id].
  static final id = obx.QueryStringProperty<InstanceRelapseLocalModel>(
    _entities[7].properties[1],
  );

  /// See [InstanceRelapseLocalModel.localId].
  static final localId = obx.QueryStringProperty<InstanceRelapseLocalModel>(
    _entities[7].properties[2],
  );

  /// See [InstanceRelapseLocalModel.description].
  static final description = obx.QueryStringProperty<InstanceRelapseLocalModel>(
    _entities[7].properties[3],
  );

  /// See [InstanceRelapseLocalModel.syncStatus].
  static final syncStatus = obx.QueryStringProperty<InstanceRelapseLocalModel>(
    _entities[7].properties[4],
  );

  /// See [InstanceRelapseLocalModel.lastSyncAttempt].
  static final lastSyncAttempt =
      obx.QueryDateProperty<InstanceRelapseLocalModel>(
        _entities[7].properties[5],
      );

  /// See [InstanceRelapseLocalModel.syncError].
  static final syncError = obx.QueryStringProperty<InstanceRelapseLocalModel>(
    _entities[7].properties[6],
  );

  /// See [InstanceRelapseLocalModel.isDeleted].
  static final isDeleted = obx.QueryBooleanProperty<InstanceRelapseLocalModel>(
    _entities[7].properties[7],
  );

  /// See [InstanceRelapseLocalModel.createdAt].
  static final createdAt = obx.QueryDateProperty<InstanceRelapseLocalModel>(
    _entities[7].properties[8],
  );

  /// See [InstanceRelapseLocalModel.updatedAt].
  static final updatedAt = obx.QueryDateProperty<InstanceRelapseLocalModel>(
    _entities[7].properties[9],
  );

  /// See [InstanceRelapseLocalModel.instanceLocalId].
  static final instanceLocalId =
      obx.QueryStringProperty<InstanceRelapseLocalModel>(
        _entities[7].properties[10],
      );
}

/// [InstanceCompanionLocalModel] entity fields to define ObjectBox queries.
class InstanceCompanionLocalModel_ {
  /// See [InstanceCompanionLocalModel.oid].
  static final oid = obx.QueryIntegerProperty<InstanceCompanionLocalModel>(
    _entities[8].properties[0],
  );

  /// See [InstanceCompanionLocalModel.id].
  static final id = obx.QueryStringProperty<InstanceCompanionLocalModel>(
    _entities[8].properties[1],
  );

  /// See [InstanceCompanionLocalModel.localId].
  static final localId = obx.QueryStringProperty<InstanceCompanionLocalModel>(
    _entities[8].properties[2],
  );

  /// See [InstanceCompanionLocalModel.instanceLocalId].
  static final instanceLocalId =
      obx.QueryStringProperty<InstanceCompanionLocalModel>(
        _entities[8].properties[3],
      );

  /// See [InstanceCompanionLocalModel.type].
  static final type = obx.QueryStringProperty<InstanceCompanionLocalModel>(
    _entities[8].properties[4],
  );

  /// See [InstanceCompanionLocalModel.userJson].
  static final userJson = obx.QueryStringProperty<InstanceCompanionLocalModel>(
    _entities[8].properties[5],
  );

  /// See [InstanceCompanionLocalModel.syncStatus].
  static final syncStatus =
      obx.QueryStringProperty<InstanceCompanionLocalModel>(
        _entities[8].properties[6],
      );

  /// See [InstanceCompanionLocalModel.lastSyncAttempt].
  static final lastSyncAttempt =
      obx.QueryDateProperty<InstanceCompanionLocalModel>(
        _entities[8].properties[7],
      );

  /// See [InstanceCompanionLocalModel.syncError].
  static final syncError = obx.QueryStringProperty<InstanceCompanionLocalModel>(
    _entities[8].properties[8],
  );

  /// See [InstanceCompanionLocalModel.isDeleted].
  static final isDeleted =
      obx.QueryBooleanProperty<InstanceCompanionLocalModel>(
        _entities[8].properties[9],
      );

  /// See [InstanceCompanionLocalModel.createdAt].
  static final createdAt = obx.QueryDateProperty<InstanceCompanionLocalModel>(
    _entities[8].properties[10],
  );

  /// See [InstanceCompanionLocalModel.updatedAt].
  static final updatedAt = obx.QueryDateProperty<InstanceCompanionLocalModel>(
    _entities[8].properties[11],
  );
}

/// [MemberLocalModel] entity fields to define ObjectBox queries.
class MemberLocalModel_ {
  /// See [MemberLocalModel.oid].
  static final oid = obx.QueryIntegerProperty<MemberLocalModel>(
    _entities[9].properties[0],
  );

  /// See [MemberLocalModel.id].
  static final id = obx.QueryStringProperty<MemberLocalModel>(
    _entities[9].properties[1],
  );

  /// See [MemberLocalModel.name].
  static final name = obx.QueryStringProperty<MemberLocalModel>(
    _entities[9].properties[2],
  );

  /// See [MemberLocalModel.email].
  static final email = obx.QueryStringProperty<MemberLocalModel>(
    _entities[9].properties[3],
  );

  /// See [MemberLocalModel.role].
  static final role = obx.QueryStringProperty<MemberLocalModel>(
    _entities[9].properties[4],
  );

  /// See [MemberLocalModel.responsibility].
  static final responsibility = obx.QueryStringProperty<MemberLocalModel>(
    _entities[9].properties[5],
  );

  /// See [MemberLocalModel.companionRole].
  static final companionRole = obx.QueryStringProperty<MemberLocalModel>(
    _entities[9].properties[6],
  );

  /// See [MemberLocalModel.phone].
  static final phone = obx.QueryStringProperty<MemberLocalModel>(
    _entities[9].properties[7],
  );

  /// See [MemberLocalModel.description].
  static final description = obx.QueryStringProperty<MemberLocalModel>(
    _entities[9].properties[8],
  );

  /// See [MemberLocalModel.createdAt].
  static final createdAt = obx.QueryDateProperty<MemberLocalModel>(
    _entities[9].properties[9],
  );

  /// See [MemberLocalModel.updatedAt].
  static final updatedAt = obx.QueryDateProperty<MemberLocalModel>(
    _entities[9].properties[10],
  );

  /// See [MemberLocalModel.healthCenterId].
  static final healthCenterId = obx.QueryStringProperty<MemberLocalModel>(
    _entities[9].properties[11],
  );
}

/// [QuestionnaireLocalModel] entity fields to define ObjectBox queries.
class QuestionnaireLocalModel_ {
  /// See [QuestionnaireLocalModel.oid].
  static final oid = obx.QueryIntegerProperty<QuestionnaireLocalModel>(
    _entities[10].properties[0],
  );

  /// See [QuestionnaireLocalModel.id].
  static final id = obx.QueryStringProperty<QuestionnaireLocalModel>(
    _entities[10].properties[1],
  );

  /// See [QuestionnaireLocalModel.question].
  static final question = obx.QueryStringProperty<QuestionnaireLocalModel>(
    _entities[10].properties[2],
  );

  /// See [QuestionnaireLocalModel.type].
  static final type = obx.QueryStringProperty<QuestionnaireLocalModel>(
    _entities[10].properties[3],
  );

  /// See [QuestionnaireLocalModel.hint].
  static final hint = obx.QueryStringProperty<QuestionnaireLocalModel>(
    _entities[10].properties[4],
  );

  /// See [QuestionnaireLocalModel.createdAt].
  static final createdAt = obx.QueryDateProperty<QuestionnaireLocalModel>(
    _entities[10].properties[5],
  );

  /// See [QuestionnaireLocalModel.updatedAt].
  static final updatedAt = obx.QueryDateProperty<QuestionnaireLocalModel>(
    _entities[10].properties[6],
  );

  /// See [QuestionnaireLocalModel.choicesJson].
  static final choicesJson = obx.QueryStringProperty<QuestionnaireLocalModel>(
    _entities[10].properties[7],
  );
}

/// [TreatmentLocalModel] entity fields to define ObjectBox queries.
class TreatmentLocalModel_ {
  /// See [TreatmentLocalModel.oid].
  static final oid = obx.QueryIntegerProperty<TreatmentLocalModel>(
    _entities[11].properties[0],
  );

  /// See [TreatmentLocalModel.id].
  static final id = obx.QueryStringProperty<TreatmentLocalModel>(
    _entities[11].properties[1],
  );

  /// See [TreatmentLocalModel.localId].
  static final localId = obx.QueryStringProperty<TreatmentLocalModel>(
    _entities[11].properties[2],
  );

  /// See [TreatmentLocalModel.instanceLocalId].
  static final instanceLocalId = obx.QueryStringProperty<TreatmentLocalModel>(
    _entities[11].properties[3],
  );

  /// See [TreatmentLocalModel.type].
  static final type = obx.QueryStringProperty<TreatmentLocalModel>(
    _entities[11].properties[4],
  );

  /// See [TreatmentLocalModel.observation].
  static final observation = obx.QueryStringProperty<TreatmentLocalModel>(
    _entities[11].properties[5],
  );

  /// See [TreatmentLocalModel.attachment].
  static final attachment = obx.QueryStringProperty<TreatmentLocalModel>(
    _entities[11].properties[6],
  );

  /// See [TreatmentLocalModel.syncStatus].
  static final syncStatus = obx.QueryStringProperty<TreatmentLocalModel>(
    _entities[11].properties[7],
  );

  /// See [TreatmentLocalModel.lastSyncAttempt].
  static final lastSyncAttempt = obx.QueryDateProperty<TreatmentLocalModel>(
    _entities[11].properties[8],
  );

  /// See [TreatmentLocalModel.syncError].
  static final syncError = obx.QueryStringProperty<TreatmentLocalModel>(
    _entities[11].properties[9],
  );

  /// See [TreatmentLocalModel.isDeleted].
  static final isDeleted = obx.QueryBooleanProperty<TreatmentLocalModel>(
    _entities[11].properties[10],
  );

  /// See [TreatmentLocalModel.createdAt].
  static final createdAt = obx.QueryDateProperty<TreatmentLocalModel>(
    _entities[11].properties[11],
  );

  /// See [TreatmentLocalModel.updatedAt].
  static final updatedAt = obx.QueryDateProperty<TreatmentLocalModel>(
    _entities[11].properties[12],
  );
}

/// [DiagnosticLocalModel] entity fields to define ObjectBox queries.
class DiagnosticLocalModel_ {
  /// See [DiagnosticLocalModel.oid].
  static final oid = obx.QueryIntegerProperty<DiagnosticLocalModel>(
    _entities[12].properties[0],
  );

  /// See [DiagnosticLocalModel.id].
  static final id = obx.QueryStringProperty<DiagnosticLocalModel>(
    _entities[12].properties[1],
  );

  /// See [DiagnosticLocalModel.localId].
  static final localId = obx.QueryStringProperty<DiagnosticLocalModel>(
    _entities[12].properties[2],
  );

  /// See [DiagnosticLocalModel.instanceLocalId].
  static final instanceLocalId = obx.QueryStringProperty<DiagnosticLocalModel>(
    _entities[12].properties[3],
  );

  /// See [DiagnosticLocalModel.questionnaireId].
  static final questionnaireId = obx.QueryStringProperty<DiagnosticLocalModel>(
    _entities[12].properties[4],
  );

  /// See [DiagnosticLocalModel.response].
  static final response = obx.QueryStringProperty<DiagnosticLocalModel>(
    _entities[12].properties[5],
  );

  /// See [DiagnosticLocalModel.syncStatus].
  static final syncStatus = obx.QueryStringProperty<DiagnosticLocalModel>(
    _entities[12].properties[6],
  );

  /// See [DiagnosticLocalModel.lastSyncAttempt].
  static final lastSyncAttempt = obx.QueryDateProperty<DiagnosticLocalModel>(
    _entities[12].properties[7],
  );

  /// See [DiagnosticLocalModel.syncError].
  static final syncError = obx.QueryStringProperty<DiagnosticLocalModel>(
    _entities[12].properties[8],
  );

  /// See [DiagnosticLocalModel.isDeleted].
  static final isDeleted = obx.QueryBooleanProperty<DiagnosticLocalModel>(
    _entities[12].properties[9],
  );

  /// See [DiagnosticLocalModel.createdAt].
  static final createdAt = obx.QueryDateProperty<DiagnosticLocalModel>(
    _entities[12].properties[10],
  );

  /// See [DiagnosticLocalModel.updatedAt].
  static final updatedAt = obx.QueryDateProperty<DiagnosticLocalModel>(
    _entities[12].properties[11],
  );
}
